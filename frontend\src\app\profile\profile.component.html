<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="bi bi-person-gear me-2"></i>Profile Settings
          </h5>
          <button 
            *ngIf="!isEditing" 
            class="btn btn-outline-primary btn-sm"
            (click)="toggleEdit()"
          >
            <i class="bi bi-pencil me-1"></i>Edit Profile
          </button>
        </div>

        <div class="card-body">
          <!-- Success Alert -->
          <div *ngIf="successMessage" class="alert alert-success" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            {{ successMessage }}
          </div>

          <!-- Error Alert -->
          <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>

          <!-- Profile Form -->
          <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
            <!-- Basic Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="bi bi-person me-2"></i>Basic Information
                </h6>
              </div>

              <div class="col-md-6 mb-3">
                <label for="fullName" class="form-label">Full Name *</label>
                <input
                  type="text"
                  class="form-control"
                  id="fullName"
                  formControlName="fullName"
                  [readonly]="!isEditing"
                  [class.is-invalid]="isFieldInvalid('fullName')"
                >
                <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
                  {{ getFieldError('fullName') }}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  formControlName="email"
                  readonly
                >
                <small class="text-muted">Email cannot be changed</small>
              </div>

              <div class="col-md-6 mb-3">
                <label for="phoneNumber" class="form-label">Phone Number</label>
                <input
                  type="tel"
                  class="form-control"
                  id="phoneNumber"
                  formControlName="phoneNumber"
                  [readonly]="!isEditing"
                  placeholder="Enter your phone number"
                >
              </div>

              <div class="col-md-6 mb-3">
                <label for="address" class="form-label">Address</label>
                <input
                  type="text"
                  class="form-control"
                  id="address"
                  formControlName="address"
                  [readonly]="!isEditing"
                  placeholder="Enter your address"
                >
              </div>
            </div>

            <!-- Doctor-specific Information -->
            <div *ngIf="currentUser?.role === 'DOCTOR'" class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="bi bi-person-badge me-2"></i>Professional Information
                </h6>
              </div>

              <div class="col-md-6 mb-3">
                <label for="specialization" class="form-label">Specialization</label>
                <input
                  type="text"
                  class="form-control"
                  id="specialization"
                  formControlName="specialization"
                  [readonly]="!isEditing"
                  placeholder="e.g., Cardiology"
                >
              </div>

              <div class="col-md-6 mb-3">
                <label for="affiliation" class="form-label">Hospital/Clinic</label>
                <input
                  type="text"
                  class="form-control"
                  id="affiliation"
                  formControlName="affiliation"
                  [readonly]="!isEditing"
                  placeholder="Your workplace"
                >
              </div>

              <div class="col-md-6 mb-3">
                <label for="yearsOfExperience" class="form-label">Years of Experience</label>
                <input
                  type="number"
                  class="form-control"
                  id="yearsOfExperience"
                  formControlName="yearsOfExperience"
                  [readonly]="!isEditing"
                  placeholder="0"
                  min="0"
                  max="50"
                >
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label">License Number</label>
                <input
                  type="text"
                  class="form-control"
                  [value]="currentUser?.licenseNumber"
                  readonly
                >
                <small class="text-muted">License number cannot be changed</small>
              </div>
            </div>

            <!-- Account Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="bi bi-shield-check me-2"></i>Account Information
                </h6>
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label">Role</label>
                <input
                  type="text"
                  class="form-control"
                  [value]="currentUser?.role | titlecase"
                  readonly
                >
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label">Member Since</label>
                <input
                  type="text"
                  class="form-control"
                  [value]="currentUser?.createdAt | date:'mediumDate'"
                  readonly
                >
              </div>
            </div>

            <!-- Action Buttons -->
            <div *ngIf="isEditing" class="d-flex gap-2">
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="isLoading || profileForm.invalid"
              >
                <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                <span *ngIf="isLoading">Saving...</span>
                <span *ngIf="!isLoading">Save Changes</span>
              </button>
              
              <button
                type="button"
                class="btn btn-outline-secondary"
                (click)="toggleEdit()"
                [disabled]="isLoading"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

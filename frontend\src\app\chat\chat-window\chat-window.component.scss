.chat-window {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; // Important for flex children
  position: relative;
  overflow: hidden; // Prevent overall overflow
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  flex-shrink: 0; // Prevent header from shrinking
  min-height: 70px; // Ensure consistent height

  .participant-info {
    display: flex;
    align-items: center;
    min-width: 0; // Allow text truncation

    .participant-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 0.75rem;
      border: 2px solid #e9ecef;
      flex-shrink: 0;
    }

    .participant-details {
      min-width: 0; // Allow text truncation

      h6 {
        font-weight: 600;
        color: #333;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .connection-status {
    flex-shrink: 0;

    .status-indicator {
      &.connected {
        color: #28a745;
      }
      &.disconnected {
        color: #dc3545;
      }
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  background-color: #f8f9fa;
  min-height: 0; // Important for flex children
  position: relative;

  // Smooth scrolling
  scroll-behavior: smooth;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.messages-list {
  .date-separator {
    text-align: center;
    margin: 1rem 0;

    .date-label {
      background-color: #e9ecef;
      padding: 0.25rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.75rem;
      color: #6c757d;
    }
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;

    .typing-dots {
      display: flex;
      align-items: center;
      background-color: #e9ecef;
      padding: 0.5rem 0.75rem;
      border-radius: 1rem;

      span {
        width: 6px;
        height: 6px;
        background-color: #6c757d;
        border-radius: 50%;
        margin: 0 2px;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
      }
    }
  }
}

.message-input-container {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background-color: white;
  flex-shrink: 0; // Prevent input from shrinking
  min-height: 80px; // Ensure consistent height

  .input-group {
    .message-input {
      border-right: none;
      border-radius: 25px 0 0 25px;
      padding: 0.75rem 1rem;
      font-size: 0.95rem;

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        border-color: #80bdff;
        outline: none;
      }

      &:disabled {
        background-color: #f8f9fa;
        opacity: 0.7;
      }

      &::placeholder {
        color: #6c757d;
        opacity: 0.8;
      }
    }

    .send-button {
      border-left: none;
      border-radius: 0 25px 25px 0;
      padding: 0.75rem 1.25rem;
      min-width: 60px;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:not(:disabled):hover {
        background-color: #0056b3;
        border-color: #0056b3;
      }

      .spinner-border-sm {
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .connection-warning {
    margin-top: 0.5rem;
    text-align: center;
    padding: 0.25rem;
    border-radius: 4px;
    background-color: #fff3cd;
  }

  .character-count {
    margin-top: 0.25rem;
    text-align: right;

    small {
      font-size: 0.75rem;
    }
  }
}

.chat-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  
  .text-center {
    max-width: 300px;
    
    i {
      color: #6c757d;
    }
    
    h5 {
      color: #495057;
    }
    
    p {
      color: #6c757d;
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .chat-window {
    height: calc(100vh - 60px); // Adjust for mobile header
  }

  .chat-header {
    padding: 0.75rem;
    min-height: 60px;

    .participant-info .participant-avatar {
      width: 35px;
      height: 35px;
    }
  }

  .messages-container {
    padding: 0.75rem 0.5rem;

    // Adjust scrollbar for mobile
    &::-webkit-scrollbar {
      width: 4px;
    }
  }

  .message-input-container {
    padding: 0.75rem;
    min-height: 70px;

    .input-group {
      .message-input {
        font-size: 16px; // Prevent zoom on iOS
        padding: 0.6rem 0.8rem;
      }

      .send-button {
        padding: 0.6rem 1rem;
        min-width: 50px;
      }
    }
  }
}

// Extra small devices
@media (max-width: 480px) {
  .chat-header {
    padding: 0.5rem;

    .participant-info {
      .participant-avatar {
        width: 30px;
        height: 30px;
        margin-right: 0.5rem;
      }

      .participant-details h6 {
        font-size: 0.9rem;
      }
    }
  }

  .messages-container {
    padding: 0.5rem 0.25rem;
  }

  .message-input-container {
    padding: 0.5rem;

    .input-group {
      .message-input {
        padding: 0.5rem 0.75rem;
      }
    }
  }
}

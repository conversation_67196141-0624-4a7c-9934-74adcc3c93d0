{"name": "healthconnect-frontend", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve": "ng serve --host 0.0.0.0 --port 4200"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/localize": "^16.2.12", "@angular/material": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@stomp/stompjs": "^7.1.1", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.10.0", "rxjs": "~7.8.0", "simple-peer": "^9.11.1", "sockjs-client": "^1.6.1", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.0", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/events": "^3.0.3", "@types/jasmine": "~4.3.0", "@types/node": "^18.7.0", "@types/simple-peer": "^9.11.8", "@types/sockjs-client": "^1.5.4", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.0"}}
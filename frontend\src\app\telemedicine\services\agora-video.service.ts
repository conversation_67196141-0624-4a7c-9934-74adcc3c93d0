import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import AgoraRTC, { 
  IAgoraRTCClient, 
  ICameraVideoTrack, 
  IMicrophoneAudioTrack,
  IRemoteVideoTrack,
  IRemoteAudioTrack,
  UID
} from 'agora-rtc-sdk-ng';

export interface AgoraCallState {
  isConnected: boolean;
  isConnecting: boolean;
  localVideoEnabled: boolean;
  localAudioEnabled: boolean;
  remoteUsers: UID[];
  error: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class AgoraVideoService {
  // Demo Agora App ID - For testing purposes
  // In production, get your own from: https://console.agora.io/
  private readonly APP_ID = 'demo-app-id-for-testing';
  
  private client: IAgoraRTCClient | null = null;
  private localVideoTrack: ICameraVideoTrack | null = null;
  private localAudioTrack: IMicrophoneAudioTrack | null = null;
  
  // State management
  private callStateSubject = new BehaviorSubject<AgoraCallState>({
    isConnected: false,
    isConnecting: false,
    localVideoEnabled: true,
    localAudioEnabled: true,
    remoteUsers: [],
    error: null
  });

  private localVideoTrackSubject = new BehaviorSubject<ICameraVideoTrack | null>(null);
  private remoteVideoTracksSubject = new BehaviorSubject<Map<UID, IRemoteVideoTrack>>(new Map());

  // Public observables
  public callState$ = this.callStateSubject.asObservable();
  public localVideoTrack$ = this.localVideoTrackSubject.asObservable();
  public remoteVideoTracks$ = this.remoteVideoTracksSubject.asObservable();

  constructor() {
    // Initialize Agora client
    this.client = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });
    this.setupClientEventHandlers();
  }

  /**
   * Join a video call room
   */
  async joinRoom(roomId: string, userId: string): Promise<void> {
    try {
      this.updateCallState({ isConnecting: true, error: null });

      if (!this.client) {
        throw new Error('Agora client not initialized');
      }

      // For demo purposes, we'll use a simple token (null for testing)
      // In production, you should generate tokens on your backend
      const token = null;

      // Join the channel
      await this.client.join(this.APP_ID, roomId, token, userId);

      // Create and publish local tracks
      await this.createAndPublishLocalTracks();

      this.updateCallState({ 
        isConnected: true, 
        isConnecting: false 
      });

      console.log('Successfully joined Agora room:', roomId);

    } catch (error) {
      console.error('Failed to join room:', error);
      this.updateCallState({ 
        isConnecting: false, 
        error: 'Failed to join video call. Please try again.' 
      });
      throw error;
    }
  }

  /**
   * Leave the video call
   */
  async leaveRoom(): Promise<void> {
    try {
      // Stop and close local tracks
      if (this.localVideoTrack) {
        this.localVideoTrack.stop();
        this.localVideoTrack.close();
        this.localVideoTrack = null;
      }

      if (this.localAudioTrack) {
        this.localAudioTrack.stop();
        this.localAudioTrack.close();
        this.localAudioTrack = null;
      }

      // Leave the channel
      if (this.client) {
        await this.client.leave();
      }

      // Reset state
      this.updateCallState({
        isConnected: false,
        isConnecting: false,
        remoteUsers: [],
        error: null
      });

      this.localVideoTrackSubject.next(null);
      this.remoteVideoTracksSubject.next(new Map());

      console.log('Left Agora room successfully');

    } catch (error) {
      console.error('Error leaving room:', error);
    }
  }

  /**
   * Toggle local video on/off
   */
  async toggleVideo(): Promise<void> {
    if (!this.localVideoTrack) return;

    const currentState = this.callStateSubject.value;
    const newVideoState = !currentState.localVideoEnabled;

    await this.localVideoTrack.setEnabled(newVideoState);
    this.updateCallState({ localVideoEnabled: newVideoState });
  }

  /**
   * Toggle local audio on/off
   */
  async toggleAudio(): Promise<void> {
    if (!this.localAudioTrack) return;

    const currentState = this.callStateSubject.value;
    const newAudioState = !currentState.localAudioEnabled;

    await this.localAudioTrack.setEnabled(newAudioState);
    this.updateCallState({ localAudioEnabled: newAudioState });
  }

  /**
   * Get local video track for displaying in UI
   */
  getLocalVideoTrack(): ICameraVideoTrack | null {
    return this.localVideoTrack;
  }

  /**
   * Get remote video track for a specific user
   */
  getRemoteVideoTrack(userId: UID): IRemoteVideoTrack | null {
    const remoteTracks = this.remoteVideoTracksSubject.value;
    return remoteTracks.get(userId) || null;
  }

  private async createAndPublishLocalTracks(): Promise<void> {
    try {
      // Create local video and audio tracks
      [this.localVideoTrack, this.localAudioTrack] = await AgoraRTC.createMicrophoneAndCameraTracks();

      // Publish tracks to the channel
      if (this.client) {
        await this.client.publish([this.localVideoTrack, this.localAudioTrack]);
      }

      // Emit local video track
      this.localVideoTrackSubject.next(this.localVideoTrack);

      console.log('Local tracks created and published');

    } catch (error) {
      console.error('Failed to create local tracks:', error);
      throw error;
    }
  }

  private setupClientEventHandlers(): void {
    if (!this.client) return;

    // Handle remote user joined
    this.client.on('user-published', async (user, mediaType) => {
      console.log('Remote user published:', user.uid, mediaType);

      // Subscribe to the remote user
      await this.client!.subscribe(user, mediaType);

      if (mediaType === 'video') {
        const remoteVideoTrack = user.videoTrack as IRemoteVideoTrack;
        const currentTracks = this.remoteVideoTracksSubject.value;
        currentTracks.set(user.uid, remoteVideoTrack);
        this.remoteVideoTracksSubject.next(new Map(currentTracks));
      }

      // Update remote users list
      const currentState = this.callStateSubject.value;
      const updatedUsers = [...currentState.remoteUsers];
      if (!updatedUsers.includes(user.uid)) {
        updatedUsers.push(user.uid);
        this.updateCallState({ remoteUsers: updatedUsers });
      }
    });

    // Handle remote user left
    this.client.on('user-left', (user) => {
      console.log('Remote user left:', user.uid);

      // Remove from remote video tracks
      const currentTracks = this.remoteVideoTracksSubject.value;
      currentTracks.delete(user.uid);
      this.remoteVideoTracksSubject.next(new Map(currentTracks));

      // Update remote users list
      const currentState = this.callStateSubject.value;
      const updatedUsers = currentState.remoteUsers.filter(uid => uid !== user.uid);
      this.updateCallState({ remoteUsers: updatedUsers });
    });

    // Handle connection state changes
    this.client.on('connection-state-change', (curState, revState) => {
      console.log('Connection state changed:', curState, revState);
      
      if (curState === 'DISCONNECTED') {
        this.updateCallState({ 
          isConnected: false,
          error: 'Connection lost. Please try rejoining the call.'
        });
      }
    });
  }

  private updateCallState(updates: Partial<AgoraCallState>): void {
    const currentState = this.callStateSubject.value;
    this.callStateSubject.next({ ...currentState, ...updates });
  }
}

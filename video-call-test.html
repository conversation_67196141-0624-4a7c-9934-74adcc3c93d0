<!DOCTYPE html>
<html>
<head>
    <title>🎥 HealthConnect Video Call Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7/bundles/stomp.umd.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
        }
        .video-container { 
            display: flex; 
            gap: 20px; 
            margin: 20px 0; 
        }
        .video-panel { 
            flex: 1; 
            border: 2px solid #333; 
            border-radius: 10px; 
            background: #000; 
            min-height: 300px; 
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        video { 
            width: 100%; 
            height: 100%; 
            border-radius: 8px; 
        }
        .controls { 
            display: flex; 
            gap: 10px; 
            margin: 10px 0; 
            flex-wrap: wrap;
        }
        button { 
            padding: 10px 15px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-weight: bold;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .status { 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .log { 
            height: 200px; 
            overflow-y: scroll; 
            border: 1px solid #ddd; 
            padding: 10px; 
            background: #f9f9f9; 
            font-family: monospace; 
            font-size: 12px;
        }
        .info-panel {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"] {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 HealthConnect Video Call Test</h1>
        
        <div class="info-panel">
            <h3>📋 Test Instructions</h3>
            <p>This test will verify the video calling feature works in the HealthConnect application.</p>
            <p><strong>Test Users:</strong></p>
            <ul>
                <li><strong>Patient:</strong> Emma Wilson (<EMAIL>) / demo123</li>
                <li><strong>Doctor:</strong> Dr. Michael Chen (<EMAIL>) / demo123</li>
            </ul>
            <p><strong>Room ID:</strong> room_ce48f8a711314d2b9e28dd07b931c5a3</p>
        </div>

        <!-- Test 1: Authentication -->
        <div class="test-section">
            <h3>🔐 Test 1: User Authentication</h3>
            <div class="controls">
                <button class="btn-primary" onclick="testPatientLogin()">Test Patient Login</button>
                <button class="btn-primary" onclick="testDoctorLogin()">Test Doctor Login</button>
            </div>
            <div id="auth-status" class="status">Ready to test authentication</div>
        </div>

        <!-- Test 2: WebSocket Connection -->
        <div class="test-section">
            <h3>🔌 Test 2: WebSocket Connection</h3>
            <div class="controls">
                <button class="btn-primary" onclick="testWebSocketConnection()">Test WebSocket</button>
                <button class="btn-danger" onclick="disconnectWebSocket()">Disconnect</button>
            </div>
            <div id="websocket-status" class="status">Ready to test WebSocket</div>
        </div>

        <!-- Test 3: Video Stream Access -->
        <div class="test-section">
            <h3>📹 Test 3: Video Stream Access</h3>
            <div class="controls">
                <button class="btn-primary" onclick="testVideoAccess()">Test Camera Access</button>
                <button class="btn-danger" onclick="stopVideoAccess()">Stop Camera</button>
            </div>
            <div id="video-status" class="status">Ready to test video access</div>
            <div class="video-container">
                <div class="video-panel">
                    <video id="test-video" autoplay muted playsinline style="display: none;"></video>
                    <div id="video-placeholder">Click "Test Camera Access" to start</div>
                </div>
            </div>
        </div>

        <!-- Test 4: WebRTC Room Join -->
        <div class="test-section">
            <h3>🎯 Test 4: WebRTC Room Join</h3>
            <div class="controls">
                <button class="btn-primary" onclick="testRoomJoin()">Join Video Room</button>
                <button class="btn-danger" onclick="leaveRoom()">Leave Room</button>
            </div>
            <div id="room-status" class="status">Ready to test room join</div>
        </div>

        <!-- Test 5: Real Application Integration -->
        <div class="test-section">
            <h3>🚀 Test 5: Real Application Integration</h3>
            <div class="controls">
                <button class="btn-success" onclick="openConsultationRoom()">Open Consultation Room</button>
                <button class="btn-primary" onclick="openDoctorDashboard()">Open Doctor Dashboard</button>
                <button class="btn-primary" onclick="openPatientDashboard()">Open Patient Dashboard</button>
            </div>
            <div id="integration-status" class="status">Ready to test real application</div>
        </div>

        <!-- Activity Log -->
        <h3>📊 Activity Log</h3>
        <div class="controls">
            <button class="btn-warning" onclick="clearLog()">Clear Log</button>
        </div>
        <div class="log" id="activity-log"></div>
    </div>

    <script>
        // Test configuration
        const TEST_CONFIG = {
            API_BASE: 'http://localhost:8080',
            WS_URL: 'http://localhost:8080/api/ws',
            FRONTEND_URL: 'http://localhost:4200',
            ROOM_ID: 'room_ce48f8a711314d2b9e28dd07b931c5a3',
            CONSULTATION_ID: 5,
            PATIENT: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Emma Wilson',
                id: 11
            },
            DOCTOR: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Dr. Michael Chen',
                id: 12
            }
        };

        // Global state
        let patientToken = null;
        let doctorToken = null;
        let stompClient = null;
        let testStream = null;

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red' : type === 'success' ? 'color: green' : 'color: blue';
            logDiv.innerHTML += `<div style="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Test 1: Authentication
        async function testPatientLogin() {
            try {
                log('🔐 Testing patient login...', 'info');
                updateStatus('auth-status', 'Testing patient login...', 'connecting');
                
                const response = await fetch(`${TEST_CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: TEST_CONFIG.PATIENT.email,
                        password: TEST_CONFIG.PATIENT.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    updateStatus('auth-status', '✅ Patient login successful', 'connected');
                    log('✅ Patient authentication successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Patient login failed: ${error.message}`, 'error');
                updateStatus('auth-status', 'Patient login failed', 'error');
            }
        }

        async function testDoctorLogin() {
            try {
                log('🔐 Testing doctor login...', 'info');
                updateStatus('auth-status', 'Testing doctor login...', 'connecting');
                
                const response = await fetch(`${TEST_CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: TEST_CONFIG.DOCTOR.email,
                        password: TEST_CONFIG.DOCTOR.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    updateStatus('auth-status', '✅ Doctor login successful', 'connected');
                    log('✅ Doctor authentication successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Doctor login failed: ${error.message}`, 'error');
                updateStatus('auth-status', 'Doctor login failed', 'error');
            }
        }

        // Test 2: WebSocket Connection
        function testWebSocketConnection() {
            if (!patientToken && !doctorToken) {
                log('❌ Please login first before testing WebSocket', 'error');
                updateStatus('websocket-status', 'Login required', 'error');
                return;
            }

            const token = patientToken || doctorToken;
            const userType = patientToken ? 'Patient' : 'Doctor';

            try {
                log(`🔌 Testing WebSocket connection for ${userType}...`, 'info');
                updateStatus('websocket-status', 'Connecting to WebSocket...', 'connecting');

                stompClient = new StompJs.Client({
                    webSocketFactory: () => new SockJS(TEST_CONFIG.WS_URL),
                    connectHeaders: {
                        Authorization: `Bearer ${token}`
                    },
                    debug: (str) => {
                        log(`STOMP: ${str}`, 'info');
                    },
                    onConnect: (frame) => {
                        log(`✅ WebSocket connected for ${userType}`, 'success');
                        updateStatus('websocket-status', '✅ WebSocket connected', 'connected');
                        
                        // Subscribe to WebRTC room
                        stompClient.subscribe(`/topic/webrtc/${TEST_CONFIG.ROOM_ID}`, (message) => {
                            const signal = JSON.parse(message.body);
                            log(`📡 Received WebRTC signal: ${signal.type}`, 'info');
                        });
                    },
                    onStompError: (frame) => {
                        log(`❌ STOMP Error: ${frame.body}`, 'error');
                        updateStatus('websocket-status', 'WebSocket connection failed', 'error');
                    },
                    onWebSocketError: (error) => {
                        log(`❌ WebSocket Error: ${error}`, 'error');
                        updateStatus('websocket-status', 'WebSocket connection failed', 'error');
                    }
                });

                stompClient.activate();
            } catch (error) {
                log(`❌ WebSocket connection failed: ${error.message}`, 'error');
                updateStatus('websocket-status', 'WebSocket connection failed', 'error');
            }
        }

        function disconnectWebSocket() {
            if (stompClient) {
                stompClient.deactivate();
                stompClient = null;
                log('🔌 WebSocket disconnected', 'info');
                updateStatus('websocket-status', 'WebSocket disconnected', 'info');
            }
        }

        // Test 3: Video Stream Access
        async function testVideoAccess() {
            try {
                log('📹 Testing camera and microphone access...', 'info');
                updateStatus('video-status', 'Requesting camera access...', 'connecting');

                testStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });

                const videoElement = document.getElementById('test-video');
                const placeholder = document.getElementById('video-placeholder');
                
                videoElement.srcObject = testStream;
                videoElement.style.display = 'block';
                placeholder.style.display = 'none';

                log('✅ Camera and microphone access granted', 'success');
                updateStatus('video-status', '✅ Video stream active', 'connected');

            } catch (error) {
                log(`❌ Camera access failed: ${error.message}`, 'error');
                updateStatus('video-status', 'Camera access denied', 'error');
            }
        }

        function stopVideoAccess() {
            if (testStream) {
                testStream.getTracks().forEach(track => track.stop());
                testStream = null;
                
                const videoElement = document.getElementById('test-video');
                const placeholder = document.getElementById('video-placeholder');
                
                videoElement.srcObject = null;
                videoElement.style.display = 'none';
                placeholder.style.display = 'block';
                placeholder.textContent = 'Camera stopped';

                log('📹 Camera stopped', 'info');
                updateStatus('video-status', 'Camera stopped', 'info');
            }
        }

        // Test 4: WebRTC Room Join
        function testRoomJoin() {
            if (!stompClient || !stompClient.connected) {
                log('❌ WebSocket connection required for room join', 'error');
                updateStatus('room-status', 'WebSocket connection required', 'error');
                return;
            }

            try {
                log('🎯 Testing WebRTC room join...', 'info');
                updateStatus('room-status', 'Joining video room...', 'connecting');

                const token = patientToken || doctorToken;
                const userId = patientToken ? TEST_CONFIG.PATIENT.id : TEST_CONFIG.DOCTOR.id;
                const userRole = patientToken ? 'PATIENT' : 'DOCTOR';

                stompClient.publish({
                    destination: `/app/webrtc/${TEST_CONFIG.ROOM_ID}/join`,
                    body: JSON.stringify({
                        userId: userId,
                        userRole: userRole
                    }),
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });

                log('✅ Room join message sent', 'success');
                updateStatus('room-status', '✅ Joined video room', 'connected');

            } catch (error) {
                log(`❌ Room join failed: ${error.message}`, 'error');
                updateStatus('room-status', 'Room join failed', 'error');
            }
        }

        function leaveRoom() {
            log('🚪 Leaving video room...', 'info');
            updateStatus('room-status', 'Left video room', 'info');
        }

        // Test 5: Real Application Integration
        function openConsultationRoom() {
            const url = `${TEST_CONFIG.FRONTEND_URL}/telemedicine/consultation-room/${TEST_CONFIG.ROOM_ID}`;
            log(`🚀 Opening consultation room: ${url}`, 'info');
            updateStatus('integration-status', 'Opening consultation room...', 'connecting');
            window.open(url, '_blank');
        }

        function openDoctorDashboard() {
            const url = `${TEST_CONFIG.FRONTEND_URL}/auth/login`;
            log(`👨‍⚕️ Opening doctor login: ${url}`, 'info');
            updateStatus('integration-status', 'Opening doctor dashboard...', 'connecting');
            window.open(url, '_blank');
        }

        function openPatientDashboard() {
            const url = `${TEST_CONFIG.FRONTEND_URL}/auth/login`;
            log(`👤 Opening patient login: ${url}`, 'info');
            updateStatus('integration-status', 'Opening patient dashboard...', 'connecting');
            window.open(url, '_blank');
        }

        // Initialize
        log('🚀 Video Call Test initialized', 'info');
        log('📋 Follow the tests in order to verify video calling functionality', 'info');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect - Complete Chat Workflow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-connecting { background-color: #ffc107; }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 5px;
        }
        .message.sent { background-color: #007bff; color: white; text-align: right; }
        .message.received { background-color: #e9ecef; }
        
        .log {
            height: 200px;
            overflow-y: auto;
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🏥 HealthConnect - Complete Chat Workflow Test</h1>
    
    <!-- Authentication Section -->
    <div class="container">
        <h2>1. Authentication</h2>
        <div class="test-section" id="auth-section">
            <h3>Login as Patient</h3>
            <input type="email" id="patient-email" value="<EMAIL>" placeholder="Patient Email">
            <input type="password" id="patient-password" value="password123" placeholder="Password">
            <button onclick="loginAsPatient()">Login as Patient</button>
            
            <h3>Login as Doctor</h3>
            <input type="email" id="doctor-email" value="<EMAIL>" placeholder="Doctor Email">
            <input type="password" id="doctor-password" value="password123" placeholder="Password">
            <button onclick="loginAsDoctor()">Login as Doctor</button>
            
            <div id="auth-status"></div>
        </div>
    </div>

    <!-- WebSocket Connection Section -->
    <div class="container">
        <h2>2. WebSocket Connection</h2>
        <div class="test-section" id="websocket-section">
            <p>Connection Status: <span class="status-indicator" id="ws-status"></span><span id="ws-status-text">Disconnected</span></p>
            <button onclick="connectWebSocket()">Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect WebSocket</button>
            <div id="ws-result"></div>
        </div>
    </div>

    <!-- Chat Creation Section -->
    <div class="container">
        <h2>3. Chat Creation</h2>
        <div class="test-section" id="chat-section">
            <input type="number" id="participant-id" placeholder="Participant ID (1 for doctor, 2 for patient)">
            <button onclick="createChat()">Create/Get Chat</button>
            <div id="chat-result"></div>
        </div>
    </div>

    <!-- Message Sending Section -->
    <div class="container">
        <h2>4. Real-time Messaging</h2>
        <div class="test-section" id="message-section">
            <div class="chat-messages" id="chat-messages"></div>
            <input type="text" id="message-input" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send Message</button>
            <div id="message-result"></div>
        </div>
    </div>

    <!-- Test Log -->
    <div class="container">
        <h2>5. Test Log</h2>
        <div class="log" id="test-log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <!-- Test Results Summary -->
    <div class="container">
        <h2>6. Test Results Summary</h2>
        <div id="test-summary" class="test-section info">
            <p>Run the tests above to see results...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.1.1/bundles/stomp.umd.min.js"></script>
    
    <script>
        // Global variables
        let authToken = null;
        let currentUser = null;
        let stompClient = null;
        let currentChatId = null;
        let testResults = {
            auth: false,
            websocket: false,
            chat: false,
            messaging: false
        };

        // Utility functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            const passed = Object.values(testResults).filter(r => r).length;
            const total = Object.keys(testResults).length;
            
            let summaryHtml = `<h3>Test Results: ${passed}/${total} Passed</h3>`;
            summaryHtml += `<ul>`;
            summaryHtml += `<li>Authentication: ${testResults.auth ? '✅ PASS' : '❌ FAIL'}</li>`;
            summaryHtml += `<li>WebSocket Connection: ${testResults.websocket ? '✅ PASS' : '❌ FAIL'}</li>`;
            summaryHtml += `<li>Chat Creation: ${testResults.chat ? '✅ PASS' : '❌ FAIL'}</li>`;
            summaryHtml += `<li>Real-time Messaging: ${testResults.messaging ? '✅ PASS' : '❌ FAIL'}</li>`;
            summaryHtml += `</ul>`;
            
            summary.innerHTML = summaryHtml;
            summary.className = `test-section ${passed === total ? 'success' : 'warning'}`;
        }

        // Authentication functions
        async function loginAsPatient() {
            await login('<EMAIL>', 'password123', 'patient');
        }

        async function loginAsDoctor() {
            await login('<EMAIL>', 'password123', 'doctor');
        }

        async function login(email, password, userType) {
            try {
                log(`Attempting to login as ${userType}...`);
                
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                if (!response.ok) {
                    throw new Error(`Login failed: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                authToken = data.token;
                currentUser = data;
                
                updateStatus('auth-section', `Successfully logged in as ${data.fullName} (${data.role})`, 'success');
                log(`✅ Login successful for ${data.fullName}`, 'success');
                testResults.auth = true;
                updateTestSummary();
                
                // Auto-connect WebSocket after successful login
                setTimeout(connectWebSocket, 1000);
                
            } catch (error) {
                updateStatus('auth-section', `Login failed: ${error.message}`, 'error');
                log(`❌ Login failed: ${error.message}`, 'error');
                testResults.auth = false;
                updateTestSummary();
            }
        }

        // WebSocket functions
        function connectWebSocket() {
            if (!authToken) {
                updateStatus('websocket-section', 'Please login first', 'error');
                return;
            }

            try {
                log('Connecting to WebSocket...');
                updateWebSocketStatus('connecting', 'Connecting...');

                stompClient = new StompJs.Client({
                    webSocketFactory: () => new SockJS('http://localhost:8080/api/ws'),
                    connectHeaders: {
                        Authorization: `Bearer ${authToken}`
                    },
                    debug: (str) => {
                        log(`STOMP Debug: ${str}`);
                    },
                    onConnect: (frame) => {
                        log('✅ WebSocket connected successfully', 'success');
                        updateStatus('websocket-section', 'WebSocket connected successfully', 'success');
                        updateWebSocketStatus('connected', 'Connected');
                        testResults.websocket = true;
                        updateTestSummary();

                        // Subscribe to error messages
                        stompClient.subscribe('/user/queue/errors', (message) => {
                            log(`❌ WebSocket Error: ${message.body}`, 'error');
                        });
                    },
                    onWebSocketClose: (event) => {
                        log('WebSocket connection closed', 'warning');
                        updateWebSocketStatus('disconnected', 'Disconnected');
                        testResults.websocket = false;
                        updateTestSummary();
                    },
                    onStompError: (frame) => {
                        log(`❌ STOMP Error: ${frame.headers.message}`, 'error');
                        updateStatus('websocket-section', `STOMP Error: ${frame.headers.message}`, 'error');
                        updateWebSocketStatus('disconnected', 'Error');
                        testResults.websocket = false;
                        updateTestSummary();
                    }
                });

                stompClient.activate();

            } catch (error) {
                log(`❌ WebSocket connection failed: ${error.message}`, 'error');
                updateStatus('websocket-section', `Connection failed: ${error.message}`, 'error');
                updateWebSocketStatus('disconnected', 'Failed');
                testResults.websocket = false;
                updateTestSummary();
            }
        }

        function disconnectWebSocket() {
            if (stompClient) {
                stompClient.deactivate();
                log('WebSocket disconnected', 'info');
                updateWebSocketStatus('disconnected', 'Disconnected');
            }
        }

        function updateWebSocketStatus(status, text) {
            const statusElement = document.getElementById('ws-status');
            const textElement = document.getElementById('ws-status-text');

            statusElement.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }

        // Chat functions
        async function createChat() {
            if (!authToken) {
                updateStatus('chat-section', 'Please login first', 'error');
                return;
            }

            const participantId = document.getElementById('participant-id').value;
            if (!participantId) {
                updateStatus('chat-section', 'Please enter participant ID', 'error');
                return;
            }

            try {
                log(`Creating chat with participant ${participantId}...`);

                const response = await fetch('http://localhost:8080/api/chats', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ participantId: parseInt(participantId) })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Chat creation failed: ${response.status} - ${errorText}`);
                }

                const chat = await response.json();
                currentChatId = chat.id;

                updateStatus('chat-section', `Chat created successfully! Chat ID: ${chat.id}`, 'success');
                log(`✅ Chat created: ID ${chat.id}`, 'success');
                testResults.chat = true;
                updateTestSummary();

                // Subscribe to chat messages
                if (stompClient && stompClient.connected) {
                    subscribeToChat(chat.id);
                }

            } catch (error) {
                updateStatus('chat-section', `Chat creation failed: ${error.message}`, 'error');
                log(`❌ Chat creation failed: ${error.message}`, 'error');
                testResults.chat = false;
                updateTestSummary();
            }
        }

        function subscribeToChat(chatId) {
            if (!stompClient || !stompClient.connected) {
                log('❌ Cannot subscribe to chat: WebSocket not connected', 'error');
                return;
            }

            try {
                // Subscribe to chat messages
                stompClient.subscribe(`/topic/chat/${chatId}`, (message) => {
                    const messageData = JSON.parse(message.body);
                    displayMessage(messageData, false);
                    log(`📨 Received message: "${messageData.content}" from ${messageData.sender.fullName}`, 'success');
                });

                // Subscribe to typing notifications
                stompClient.subscribe(`/topic/chat/${chatId}/typing`, (message) => {
                    const typingData = JSON.parse(message.body);
                    log(`⌨️ ${typingData.userEmail} is ${typingData.status}`, 'info');
                });

                log(`✅ Subscribed to chat ${chatId}`, 'success');

            } catch (error) {
                log(`❌ Failed to subscribe to chat: ${error.message}`, 'error');
            }
        }

        // Messaging functions
        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const content = messageInput.value.trim();

            if (!content) {
                updateStatus('message-section', 'Please enter a message', 'error');
                return;
            }

            if (!currentChatId) {
                updateStatus('message-section', 'Please create a chat first', 'error');
                return;
            }

            if (!stompClient || !stompClient.connected) {
                updateStatus('message-section', 'WebSocket not connected', 'error');
                return;
            }

            try {
                log(`Sending message: "${content}"`);

                const messageRequest = {
                    chatId: currentChatId,
                    content: content
                };

                stompClient.publish({
                    destination: `/app/chat/${currentChatId}/send`,
                    body: JSON.stringify(messageRequest),
                    headers: {
                        Authorization: `Bearer ${authToken}`
                    }
                });

                // Display sent message immediately
                displayMessage({
                    content: content,
                    sender: currentUser,
                    createdAt: new Date().toISOString()
                }, true);

                messageInput.value = '';
                updateStatus('message-section', 'Message sent successfully', 'success');
                log(`✅ Message sent successfully`, 'success');
                testResults.messaging = true;
                updateTestSummary();

            } catch (error) {
                updateStatus('message-section', `Failed to send message: ${error.message}`, 'error');
                log(`❌ Failed to send message: ${error.message}`, 'error');
                testResults.messaging = false;
                updateTestSummary();
            }
        }

        function displayMessage(message, isSent) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;

            const time = new Date(message.createdAt).toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>${message.sender.fullName}:</strong> ${message.content}
                <br><small>${time}</small>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Auto-run basic connectivity test on page load
        window.onload = function() {
            log('🚀 HealthConnect Chat Workflow Test loaded', 'info');
            log('📋 Instructions:', 'info');
            log('1. Login as Patient or Doctor', 'info');
            log('2. WebSocket will auto-connect after login', 'info');
            log('3. Create a chat (use ID 1 for doctor, 2 for patient)', 'info');
            log('4. Send messages to test real-time communication', 'info');
            updateTestSummary();
        };

    </script>
</body>
</html>

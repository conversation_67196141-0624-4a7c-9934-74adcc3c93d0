<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect Video Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; border: none; }
        .btn-success { background-color: #28a745; color: white; border: none; }
        .btn-danger { background-color: #dc3545; color: white; border: none; }
        #log { height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; }
        .log-entry { margin: 2px 0; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 HealthConnect Video Integration Test</h1>
        
        <div class="section info">
            <h3>Test Instructions</h3>
            <p>This page tests the complete video consultation integration:</p>
            <ol>
                <li>Login as patient and doctor</li>
                <li>Create video appointment</li>
                <li>Create video consultation</li>
                <li>Test frontend URLs</li>
            </ol>
        </div>

        <div class="section">
            <h3>🔐 Authentication</h3>
            <button class="btn-primary" onclick="loginPatient()">Login Patient</button>
            <button class="btn-primary" onclick="loginDoctor()">Login Doctor</button>
            <div id="auth-status"></div>
        </div>

        <div class="section">
            <h3>📅 Appointment & Consultation</h3>
            <button class="btn-success" onclick="createVideoAppointment()" id="create-appointment" disabled>Create Video Appointment</button>
            <button class="btn-success" onclick="createVideoConsultation()" id="create-consultation" disabled>Create Video Consultation</button>
            <div id="appointment-status"></div>
        </div>

        <div class="section">
            <h3>🎥 Frontend Integration</h3>
            <button class="btn-primary" onclick="openConsultationPage()" id="open-consultation" disabled>Open Consultation Page</button>
            <button class="btn-primary" onclick="openRoomPage()" id="open-room" disabled>Open Room Page</button>
            <div id="frontend-status"></div>
        </div>

        <div class="section">
            <h3>📋 Test Log</h3>
            <div id="log"></div>
            <button class="btn-danger" onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        const FRONTEND_BASE = 'http://localhost:4200';
        
        let patientToken = null;
        let doctorToken = null;
        let appointmentId = null;
        let consultationId = null;
        let roomId = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `section ${type}`;
            element.innerHTML = `<p>${message}</p>`;
        }

        async function loginPatient() {
            try {
                log('🔐 Logging in as patient...', 'info');
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    log('✅ Patient login successful', 'success');
                    updateStatus('auth-status', '✅ Patient logged in successfully', 'success');
                    document.getElementById('create-appointment').disabled = false;
                } else {
                    throw new Error(`Login failed: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Patient login failed: ${error.message}`, 'error');
                updateStatus('auth-status', `❌ Patient login failed: ${error.message}`, 'error');
            }
        }

        async function loginDoctor() {
            try {
                log('🔐 Logging in as doctor...', 'info');
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    log('✅ Doctor login successful', 'success');
                    updateStatus('auth-status', '✅ Both users logged in successfully', 'success');
                } else {
                    throw new Error(`Login failed: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Doctor login failed: ${error.message}`, 'error');
            }
        }

        async function createVideoAppointment() {
            try {
                log('📅 Creating video appointment...', 'info');
                
                // First get doctors list
                const doctorsResponse = await fetch(`${API_BASE}/users/doctors`, {
                    headers: { 'Authorization': `Bearer ${patientToken}` }
                });
                
                if (!doctorsResponse.ok) {
                    throw new Error('Failed to get doctors list');
                }
                
                const doctors = await doctorsResponse.json();
                const doctorId = doctors[0].id;
                
                // Create appointment - use a date 2 days in the future to avoid validation issues
                const futureDate = new Date();
                futureDate.setDate(futureDate.getDate() + 2);

                const appointmentData = {
                    doctorId: doctorId,
                    date: futureDate.toISOString().split('T')[0],
                    startTime: '14:00',
                    endTime: '14:30',
                    type: 'VIDEO_CALL',
                    reasonForVisit: 'Video consultation integration test'
                };

                const response = await fetch(`${API_BASE}/appointments`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${patientToken}`
                    },
                    body: JSON.stringify(appointmentData)
                });

                if (response.ok) {
                    const appointment = await response.json();
                    appointmentId = appointment.id;
                    log(`✅ Video appointment created: ID ${appointmentId}`, 'success');
                    updateStatus('appointment-status', `✅ Video appointment created: ID ${appointmentId}`, 'success');
                    document.getElementById('create-consultation').disabled = false;
                } else {
                    throw new Error(`Failed to create appointment: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Failed to create appointment: ${error.message}`, 'error');
                updateStatus('appointment-status', `❌ Failed to create appointment: ${error.message}`, 'error');
            }
        }

        async function createVideoConsultation() {
            try {
                log('🎥 Creating video consultation...', 'info');
                
                const consultationData = {
                    appointmentId: appointmentId,
                    type: 'ROUTINE_CHECKUP'
                };

                const response = await fetch(`${API_BASE}/video-consultation/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${patientToken}`
                    },
                    body: JSON.stringify(consultationData)
                });

                if (response.ok) {
                    const consultation = await response.json();
                    consultationId = consultation.id;
                    roomId = consultation.roomId;
                    log(`✅ Video consultation created: ID ${consultationId}, Room: ${roomId}`, 'success');
                    updateStatus('appointment-status', `✅ Video consultation created: ID ${consultationId}`, 'success');
                    document.getElementById('open-consultation').disabled = false;
                    document.getElementById('open-room').disabled = false;
                } else {
                    throw new Error(`Failed to create consultation: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Failed to create consultation: ${error.message}`, 'error');
                updateStatus('appointment-status', `❌ Failed to create consultation: ${error.message}`, 'error');
            }
        }

        function openConsultationPage() {
            const url = `${FRONTEND_BASE}/telemedicine/consultation/${consultationId}`;
            log(`🌐 Opening consultation page: ${url}`, 'info');
            window.open(url, '_blank');
            updateStatus('frontend-status', `✅ Consultation page opened in new tab`, 'success');
        }

        function openRoomPage() {
            const url = `${FRONTEND_BASE}/telemedicine/room/${roomId}`;
            log(`🎥 Opening room page: ${url}`, 'info');
            window.open(url, '_blank');
            updateStatus('frontend-status', `✅ Room page opened in new tab`, 'success');
        }

        // Initialize
        log('🏥 HealthConnect Video Integration Test initialized', 'info');
        log('📋 Follow the steps to test the complete video consultation workflow', 'info');
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Real-Time Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7/bundles/stomp.umd.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #log { height: 300px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        input[type="text"] { width: 300px; padding: 5px; }
    </style>
</head>
<body>
    <h1>🧪 HealthConnect Real-Time Features Test</h1>
    
    <div class="test-section">
        <h3>1. Authentication</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="password123">
        <button onclick="login()">Login</button>
        <div id="auth-status"></div>
    </div>

    <div class="test-section">
        <h3>2. WebSocket Connection</h3>
        <button onclick="connectWebSocket()">Connect WebSocket</button>
        <button onclick="disconnectWebSocket()">Disconnect</button>
        <div id="ws-status"></div>
    </div>

    <div class="test-section">
        <h3>3. Real-Time Chat Test</h3>
        <input type="number" id="chatId" placeholder="Chat ID" value="1">
        <input type="text" id="messageText" placeholder="Test message">
        <button onclick="sendTestMessage()">Send Message</button>
        <button onclick="subscribeToChatTest()">Subscribe to Chat</button>
    </div>

    <div class="test-section">
        <h3>4. WebRTC Test</h3>
        <input type="text" id="roomId" placeholder="Room ID" value="room_test123">
        <button onclick="testWebRTC()">Test WebRTC Signaling</button>
    </div>

    <div class="test-section">
        <h3>Test Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log"></div>
    </div>

    <script>
        let stompClient = null;
        let authToken = null;
        const API_BASE = 'http://localhost:8080';
        const WS_URL = 'http://localhost:8080/api/ws';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                log('🔐 Attempting login...', 'info');
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    document.getElementById('auth-status').innerHTML = '<span class="success">✅ Logged in successfully</span>';
                    log('✅ Login successful', 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('auth-status').innerHTML = '<span class="error">❌ Login failed</span>';
                    log(`❌ Login failed: ${error}`, 'error');
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
            }
        }

        function connectWebSocket() {
            if (!authToken) {
                log('❌ Please login first', 'error');
                return;
            }

            log('🔌 Connecting to WebSocket...', 'info');
            
            stompClient = new StompJs.Client({
                webSocketFactory: () => new SockJS(WS_URL),
                connectHeaders: {
                    Authorization: `Bearer ${authToken}`
                },
                debug: (str) => {
                    log(`STOMP: ${str}`, 'info');
                },
                onConnect: (frame) => {
                    document.getElementById('ws-status').innerHTML = '<span class="success">✅ WebSocket Connected</span>';
                    log('✅ WebSocket connected successfully!', 'success');
                    log(`Session: ${frame.headers['session']}`, 'info');
                    
                    // Subscribe to test topic
                    stompClient.subscribe('/topic/test', (message) => {
                        log(`📨 Test message received: ${message.body}`, 'success');
                    });
                },
                onStompError: (frame) => {
                    document.getElementById('ws-status').innerHTML = '<span class="error">❌ STOMP Error</span>';
                    log(`❌ STOMP Error: ${frame.body}`, 'error');
                },
                onWebSocketError: (error) => {
                    document.getElementById('ws-status').innerHTML = '<span class="error">❌ WebSocket Error</span>';
                    log(`❌ WebSocket Error: ${error}`, 'error');
                }
            });

            stompClient.activate();
        }

        function disconnectWebSocket() {
            if (stompClient) {
                stompClient.deactivate();
                document.getElementById('ws-status').innerHTML = '<span class="info">Disconnected</span>';
                log('🔌 WebSocket disconnected', 'info');
            }
        }

        function subscribeToChatTest() {
            if (!stompClient || !stompClient.connected) {
                log('❌ WebSocket not connected', 'error');
                return;
            }

            const chatId = document.getElementById('chatId').value;
            log(`📡 Subscribing to chat ${chatId}...`, 'info');

            stompClient.subscribe(`/topic/chat/${chatId}`, (message) => {
                const msg = JSON.parse(message.body);
                log(`💬 Chat message received: ${msg.content} from ${msg.sender.fullName}`, 'success');
            });

            stompClient.subscribe(`/topic/chat/${chatId}/typing`, (message) => {
                const typing = JSON.parse(message.body);
                log(`⌨️ Typing notification: ${typing.userEmail} is ${typing.status}`, 'info');
            });
        }

        function sendTestMessage() {
            if (!stompClient || !stompClient.connected) {
                log('❌ WebSocket not connected', 'error');
                return;
            }

            const chatId = document.getElementById('chatId').value;
            const messageText = document.getElementById('messageText').value;

            if (!messageText) {
                log('❌ Please enter a message', 'error');
                return;
            }

            log(`📤 Sending message to chat ${chatId}: ${messageText}`, 'info');

            stompClient.publish({
                destination: `/app/chat/${chatId}/send`,
                body: JSON.stringify({ content: messageText }),
                headers: {
                    Authorization: `Bearer ${authToken}`
                }
            });
        }

        function testWebRTC() {
            if (!stompClient || !stompClient.connected) {
                log('❌ WebSocket not connected', 'error');
                return;
            }

            const roomId = document.getElementById('roomId').value;
            log(`🎥 Testing WebRTC signaling for room: ${roomId}`, 'info');

            // Subscribe to WebRTC messages
            stompClient.subscribe(`/topic/webrtc/${roomId}`, (message) => {
                const signal = JSON.parse(message.body);
                log(`📡 WebRTC signal received: ${signal.type}`, 'success');
            });

            // Send join message
            stompClient.publish({
                destination: `/app/webrtc/${roomId}/join`,
                body: JSON.stringify({
                    userId: 1,
                    userRole: 'PATIENT'
                }),
                headers: {
                    Authorization: `Bearer ${authToken}`
                }
            });
        }

        // Auto-clear log when it gets too long
        setInterval(() => {
            const logDiv = document.getElementById('log');
            const lines = logDiv.children.length;
            if (lines > 100) {
                for (let i = 0; i < 50; i++) {
                    logDiv.removeChild(logDiv.firstChild);
                }
            }
        }, 5000);

        log('🚀 Real-time test page loaded. Please login first, then test WebSocket connection.', 'info');
    </script>
</body>
</html>

# Production Configuration
spring.application.name=healthconnect-backend
server.port=8080

# PostgreSQL Database Configuration
spring.datasource.url=**********************************************
spring.datasource.username=healthconnect_user
spring.datasource.password=healthconnect_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration for Production
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.defer-datasource-initialization=false

# JWT Configuration
jwt.secret=${JWT_SECRET:mySecretKeyForHealthConnectPlatformThatIsLongEnoughForSecurity}
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Google Gemini AI Configuration
google.ai.api.key=${GOOGLE_AI_API_KEY:AIzaSyCFWc5GegGFmlMpD7-gNyfXO_eiU24PklQ}
google.ai.model=gemini-1.5-flash

# Logging Configuration
logging.level.com.healthconnect=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web.cors=WARN
logging.level.org.springframework.web.socket=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Error handling
server.error.include-message=always
server.error.include-binding-errors=always

# Allow circular references temporarily
spring.main.allow-circular-references=true

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.file.upload-dir=${FILE_UPLOAD_DIR:/var/healthconnect/uploads}
app.file.max-size=10485760
app.file.allowed-types=image/jpeg,image/png,image/gif,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,audio/mpeg,audio/wav,video/mp4

# WebSocket Configuration
spring.websocket.max-connections=1000
spring.websocket.heartbeat-interval=30000

# Presence Service Configuration
app.presence.cleanup-interval=300000
app.presence.inactive-timeout=600000

# CORS Configuration
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:4200,https://yourdomain.com}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# Security Configuration
server.ssl.enabled=false
# For production, enable SSL:
# server.ssl.enabled=true
# server.ssl.key-store=classpath:keystore.p12
# server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD}
# server.ssl.key-store-type=PKCS12
# server.ssl.key-alias=healthconnect

# Health Check Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true

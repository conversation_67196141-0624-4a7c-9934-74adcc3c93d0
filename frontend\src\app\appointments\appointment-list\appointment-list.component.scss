// Premium Appointment List Cards
.appointment-card {
  border: none;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  box-shadow:
    0 8px 32px rgba(20, 184, 166, 0.08),
    0 4px 16px rgba(20, 184, 166, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #0d9488, #0f766e);
    border-radius: 0 2px 2px 0;
  }
}

.appointment-card:hover {
  transform: translateY(-6px);
  box-shadow:
    0 16px 48px rgba(20, 184, 166, 0.15),
    0 8px 24px rgba(20, 184, 166, 0.1);
}

.appointment-details {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 500;
}

.appointment-details i {
  width: 18px;
  text-align: center;
  color: #0d9488;
  font-weight: 600;
}

// Premium Status Badges for List
.badge-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.badge-info {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

.badge-primary {
  background: linear-gradient(135deg, #0d9488, #0f766e);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
}

.badge-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.badge-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.badge-secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
}

.btn-outline-danger {
  border-color: #e74a3b;
  color: #e74a3b;
}

.btn-outline-danger:hover {
  background-color: #e74a3b;
  border-color: #e74a3b;
}

.btn-outline-secondary {
  border-color: #858796;
  color: #858796;
}

.btn-outline-secondary:hover {
  background-color: #858796;
  border-color: #858796;
}

.card {
  border: 1px solid #e3e6f0;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-title {
  color: #5a5c69;
  font-weight: 600;
}

.alert-danger {
  border-left: 4px solid #e74a3b;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Premium HealthConnect Appointment Details Design
.info-card {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  border: 2px solid rgba(20, 184, 166, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  box-shadow:
    0 8px 32px rgba(20, 184, 166, 0.08),
    0 4px 16px rgba(20, 184, 166, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #0d9488, #0f766e);
    border-radius: 0 2px 2px 0;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow:
      0 16px 48px rgba(20, 184, 166, 0.15),
      0 8px 24px rgba(20, 184, 166, 0.1);
    border-color: rgba(20, 184, 166, 0.2);
  }
}

.info-title {
  color: #374151;
  font-weight: 700;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  letter-spacing: -0.025em;

  i {
    color: #0d9488;
    margin-right: 0.5rem;
    font-size: 1.1rem;
  }
}

.info-value {
  color: #1f2937;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.4;
}

.info-subtitle {
  color: #6b7280;
  font-size: 0.95rem;
  margin-bottom: 0;
  font-weight: 500;
}

// Premium Status Badges
.badge-lg {
  font-size: 0.95rem;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  letter-spacing: 0.025em;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-color: rgba(245, 158, 11, 0.3);
}

.badge-info {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  border-color: rgba(14, 165, 233, 0.3);
}

.badge-primary {
  background: linear-gradient(135deg, #0d9488, #0f766e);
  color: white;
  border-color: rgba(20, 184, 166, 0.3);
}

.badge-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-color: rgba(16, 185, 129, 0.3);
}

.badge-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-color: rgba(239, 68, 68, 0.3);
}

.badge-secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border-color: rgba(107, 114, 128, 0.3);
}

// Premium Form Controls
.form-select,
.form-control {
  border: 2px solid rgba(20, 184, 166, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 1rem;
  color: #374151;
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &::placeholder {
    color: #9ca3af;
    opacity: 0.8;
  }
}

.form-select:focus,
.form-control:focus {
  background: #ffffff;
  border-color: #0d9488;
  box-shadow:
    0 0 0 4px rgba(20, 184, 166, 0.1),
    0 4px 12px rgba(20, 184, 166, 0.15);
  transform: translateY(-2px);
  outline: none;
}

.form-label {
  color: #374151;
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

// Premium Button Styles
.btn-primary {
  background: linear-gradient(135deg, #0d9488, #0f766e);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  color: white;
  box-shadow:
    0 8px 24px rgba(20, 184, 166, 0.3),
    0 4px 12px rgba(20, 184, 166, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 32px rgba(20, 184, 166, 0.4),
      0 8px 16px rgba(20, 184, 166, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-outline-primary {
  border: 2px solid #0d9488;
  color: #0d9488;
  border-radius: 12px;
  padding: 10px 22px;
  font-weight: 600;
  background: transparent;
  transition: all 0.3s ease;

  &:hover {
    background: #0d9488;
    border-color: #0d9488;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(20, 184, 166, 0.3);
  }
}

.btn-outline-danger {
  border: 2px solid #ef4444;
  color: #ef4444;
  border-radius: 12px;
  padding: 10px 22px;
  font-weight: 600;
  background: transparent;
  transition: all 0.3s ease;

  &:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(239, 68, 68, 0.3);
  }
}

.btn-outline-secondary {
  border: 2px solid #6b7280;
  color: #6b7280;
  border-radius: 12px;
  padding: 10px 22px;
  font-weight: 600;
  background: transparent;
  transition: all 0.3s ease;

  &:hover {
    background: #6b7280;
    border-color: #6b7280;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(107, 114, 128, 0.3);
  }
}

// Premium Card Design
.card {
  border: none;
  border-radius: 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  box-shadow:
    0 20px 60px rgba(20, 184, 166, 0.08),
    0 8px 32px rgba(20, 184, 166, 0.04);
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d9488, #0f766e, #14b8a6);
  }
}

.card-header {
  background: transparent;
  border-bottom: 2px solid rgba(20, 184, 166, 0.1);
  padding: 2rem 2rem 1.5rem;
}

.card-body {
  padding: 2rem;
}

.card-title {
  color: #1f2937;
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.025em;

  i {
    color: #0d9488;
    margin-right: 0.75rem;
  }
}

// Premium Alerts
.alert-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
  border: 2px solid rgba(16, 185, 129, 0.2);
  border-radius: 16px;
  color: #065f46;
  padding: 1rem 1.5rem;

  i {
    color: #10b981;
  }
}

.alert-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
  border: 2px solid rgba(239, 68, 68, 0.2);
  border-radius: 16px;
  color: #7f1d1d;
  padding: 1rem 1.5rem;

  i {
    color: #ef4444;
  }
}

// Premium Loading Spinner
.spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
  border-color: #0d9488;
  border-right-color: transparent;
}

// Spin animation for Bootstrap Icons
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Premium Action Buttons Layout
.appointment-actions-container {
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.9));
  border: 2px solid rgba(20, 184, 166, 0.1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(20, 184, 166, 0.08);
}

.back-button-section {
  margin-bottom: 1.5rem;
}

.action-buttons-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-buttons-group,
.video-buttons-group,
.management-buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

// Custom Teal Button Styles
.btn-teal {
  background: linear-gradient(135deg, #0d9488, #0f766e);
  border: none;
  border-radius: 12px;
  padding: 10px 20px;
  font-weight: 600;
  color: white;
  box-shadow: 0 6px 20px rgba(20, 184, 166, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 28px rgba(20, 184, 166, 0.35);
    background: linear-gradient(135deg, #0f766e, #0d9488);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 4px 12px rgba(20, 184, 166, 0.15);
  }
}

.btn-teal-outline {
  border: 2px solid #0d9488;
  color: #0d9488;
  border-radius: 12px;
  padding: 8px 18px;
  font-weight: 600;
  background: transparent;
  transition: all 0.3s ease;

  &:hover {
    background: #0d9488;
    border-color: #0d9488;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(20, 184, 166, 0.25);
  }

  &:active {
    transform: translateY(0);
  }
}

// Premium responsive design
@media (max-width: 768px) {
  .info-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    padding: 1.5rem 1.5rem 1rem;
  }

  .info-title {
    font-size: 0.9rem;
  }

  .info-value {
    font-size: 1.1rem;
  }

  .btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .appointment-actions-container {
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .action-buttons-section {
    gap: 0.75rem;
  }

  .chat-buttons-group,
  .video-buttons-group,
  .management-buttons-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .chat-buttons-group .btn,
  .video-buttons-group .btn,
  .management-buttons-group .btn {
    width: 100%;
    justify-content: center;
  }
}

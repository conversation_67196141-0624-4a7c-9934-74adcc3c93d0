// Clean AI Chat Container - <PERSON>l & White Only
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  background: var(--hc-white);
  position: relative;
  overflow: hidden;
}

// Compact Header Design
.chat-header {
  background: var(--hc-white);
  border-bottom: 1px solid var(--hc-primary-100);
  padding: 1rem 2rem;
  position: relative;
  z-index: 10;
  box-shadow: var(--hc-shadow-sm);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 1rem;

      .ai-avatar {
        position: relative;

        .avatar-glow {
          width: 40px;
          height: 40px;
          background: var(--hc-primary-600);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--hc-white);
          font-size: 1rem;

          i {
            font-size: 0.9rem;
          }
        }
      }

      .header-text {
        .assistant-title {
          color: var(--hc-primary-700);
          font-size: 1.2rem;
          font-weight: 600;
          margin: 0;
        }

        .assistant-subtitle {
          color: var(--hc-gray-600);
          font-size: 0.85rem;
          margin: 0;
          font-weight: 400;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 0.75rem;

      .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
        padding: 0.75rem 1.25rem;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        i {
          font-size: 0.85rem;
        }
      }
    }
  }

  .conversation-status {
    margin-top: 1rem;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.1);

      .status-dot {
        width: 8px;
        height: 8px;
        background: #4ade80;
        border-radius: 50%;
        animation: pulse 2s ease-in-out infinite;
      }

      .status-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        font-weight: 500;
      }

      .conversation-title {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.85rem;
        margin-left: auto;
      }
    }
  }
}

// Messages Container
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  position: relative;
  z-index: 5;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Loading State
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .loading-animation {
    text-align: center;

    .loading-dots {
      display: flex;
      gap: 8px;
      justify-content: center;
      margin-bottom: 1rem;

      .dot {
        width: 12px;
        height: 12px;
        background: linear-gradient(135deg, var(--hc-primary-500) 0%, var(--hc-primary-600) 100%);
        border-radius: 50%;
        animation: loading-bounce 1.4s ease-in-out infinite both;

        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
      }
    }

    .loading-text {
      color: var(--hc-gray-600);
      font-size: 0.9rem;
      margin: 0;
    }
  }
}

// Welcome Section - Compact Design
.welcome-section {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
  padding: 2rem 2rem 1rem 2rem;

  .welcome-content {
    .welcome-icon {
      margin-bottom: 1.5rem;

      .icon-wrapper {
        width: 60px;
        height: 60px;
        background: var(--hc-primary-600);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 4px 16px rgba(20, 184, 166, 0.2);

        i {
          font-size: 1.5rem;
          color: var(--hc-white);
        }
      }
    }

    .welcome-title {
      color: var(--hc-primary-700);
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.75rem;
    }

    .welcome-description {
      color: var(--hc-gray-600);
      font-size: 1rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }

    .disclaimer-card {
      background: var(--hc-primary-50);
      border: 1px solid var(--hc-primary-200);
      border-radius: 12px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      text-align: left;

      .disclaimer-icon {
        width: 40px;
        height: 40px;
        background: var(--hc-primary-100);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        i {
          color: var(--hc-primary-600);
          font-size: 1.1rem;
        }
      }

      .disclaimer-content {
        h6 {
          color: var(--hc-primary-700);
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        p {
          color: var(--hc-gray-600);
          font-size: 0.9rem;
          line-height: 1.5;
          margin: 0;
        }
      }
    }

    .quick-actions {
      text-align: left;

      h6 {
        color: var(--hc-gray-700);
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .action-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;

        .chip {
          background: var(--hc-white);
          border: 1px solid var(--hc-primary-200);
          color: var(--hc-primary-700);
          padding: 0.75rem 1rem;
          border-radius: 25px;
          font-size: 0.85rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            background: var(--hc-primary-50);
            border-color: var(--hc-primary-300);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(20, 184, 166, 0.15);
          }

          i {
            font-size: 0.8rem;
            color: var(--hc-primary-600);
          }
        }
      }
    }
  }
}

// Messages List
.messages-list {
  max-width: 900px;
  margin: 0 auto;

  .message-wrapper {
    margin-bottom: 2rem;
    display: flex;

    &.user-message {
      justify-content: flex-end;

      .message-bubble {
        max-width: 70%;

        .message-content {
          background: var(--hc-primary-600);
          color: var(--hc-white);
          border-radius: 20px 20px 5px 20px;
          padding: 1rem 1.25rem;
          box-shadow: 0 8px 32px rgba(20, 184, 166, 0.3);

          .message-body {
            .message-text {
              font-size: 0.95rem;
              line-height: 1.5;
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
            }
          }

          .message-footer {
            margin-top: 0.5rem;
            text-align: right;

            .message-timestamp {
              font-size: 0.75rem;
              opacity: 0.8;
            }
          }
        }
      }
    }

    &.ai-message {
      justify-content: flex-start;

      .message-bubble {
        max-width: 80%;
        display: flex;
        gap: 0.75rem;

        .message-avatar {
          flex-shrink: 0;
          margin-top: 0.25rem;

          .ai-avatar-small {
            width: 36px;
            height: 36px;
            background: var(--hc-primary-600);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--hc-white);
            font-size: 0.9rem;
            box-shadow: 0 4px 16px rgba(20, 184, 166, 0.3);

            &.typing {
              animation: pulse-glow 2s ease-in-out infinite;
            }

            i {
              filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
            }
          }
        }

        .message-content {
          background: var(--hc-white);
          border: 1px solid var(--hc-primary-100);
          border-radius: 20px 20px 20px 5px;
          padding: 1rem 1.25rem;
          box-shadow: 0 8px 32px rgba(20, 184, 166, 0.1);

          .message-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;

            .sender-name {
              color: var(--hc-primary-700);
              font-weight: 600;
              font-size: 0.85rem;
            }

            .message-timestamp, .typing-status {
              color: var(--hc-gray-500);
              font-size: 0.75rem;
            }
          }

          .message-body {
            .message-text {
              color: var(--hc-gray-700);
              font-size: 0.95rem;
              line-height: 1.6;
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
            }
          }
        }
      }

      &.typing-message {
        .message-content {
          .message-body {
            .modern-typing-indicator {
              display: flex;
              align-items: center;
              gap: 6px;
              padding: 0.5rem 0;

              .typing-dot {
                width: 8px;
                height: 8px;
                background: var(--hc-primary-400);
                border-radius: 50%;
                animation: typing-bounce 1.4s ease-in-out infinite both;

                &:nth-child(1) { animation-delay: -0.32s; }
                &:nth-child(2) { animation-delay: -0.16s; }
                &:nth-child(3) { animation-delay: 0s; }
              }
            }
          }
        }
      }
    }
  }
}

// Error Notification
.error-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;

  .error-content {
    background: rgba(239, 68, 68, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.2);

    .error-icon {
      color: white;
      font-size: 1.1rem;
      margin-top: 0.1rem;
    }

    .error-text {
      flex: 1;

      h6 {
        color: white;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
      }

      p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.85rem;
        margin: 0;
        line-height: 1.4;
      }
    }

    .error-close {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }
  }
}

// Clean Chat Input Section
.chat-input-section {
  background: var(--hc-white);
  border-top: 1px solid var(--hc-primary-100);
  padding: 1.5rem 2rem;
  position: relative;
  z-index: 10;

  .conversation-selector {
    margin-bottom: 1rem;

    .selector-wrapper {
      max-width: 350px;

      .selector-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--hc-gray-700);
        font-size: 0.85rem;
        font-weight: 500;
        margin-bottom: 0.5rem;

        i {
          font-size: 0.8rem;
          color: var(--hc-primary-600);
        }
      }

      .modern-select {
        width: 100%;
        background: var(--hc-white);
        border: 1px solid var(--hc-primary-200);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        color: var(--hc-gray-700);
        font-size: 0.9rem;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--hc-primary-400);
          box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
          background: var(--hc-primary-50);
        }

        option {
          background: var(--hc-white);
          color: var(--hc-gray-700);
        }
      }
    }
  }

  .input-container {
    .input-wrapper {
      display: flex;
      align-items: flex-end;
      background: var(--hc-white);
      border: 1px solid var(--hc-primary-200);
      border-radius: 20px;
      padding: 0.75rem 1rem;
      transition: all 0.3s ease;

      &:focus-within {
        border-color: var(--hc-primary-400);
        box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
        background: var(--hc-primary-50);
      }

      .message-input {
        flex: 1;
        background: none;
        border: none;
        color: var(--hc-gray-700);
        font-size: 0.95rem;
        line-height: 1.5;
        resize: none;
        min-height: 24px;
        max-height: 120px;

        &::placeholder {
          color: var(--hc-gray-400);
        }

        &:focus {
          outline: none;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .input-actions {
        margin-left: 0.75rem;

        .send-button {
          width: 40px;
          height: 40px;
          background: var(--hc-primary-600);
          border: none;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--hc-white);
          font-size: 0.9rem;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 16px rgba(20, 184, 166, 0.3);

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(20, 184, 166, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }

          &.sending {
            animation: pulse-glow 2s ease-in-out infinite;
          }

          .button-content {
            display: flex;
            align-items: center;
            justify-content: center;

            .sending-spinner {
              display: flex;
              gap: 3px;

              .spinner-dot {
                width: 4px;
                height: 4px;
                background: white;
                border-radius: 50%;
                animation: spinner-bounce 1.4s ease-in-out infinite both;

                &:nth-child(1) { animation-delay: -0.32s; }
                &:nth-child(2) { animation-delay: -0.16s; }
                &:nth-child(3) { animation-delay: 0s; }
              }
            }
          }
        }
      }
    }

    .input-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 0.5rem;

      .character-count {
        .count {
          color: rgba(255, 255, 255, 0.5);
          font-size: 0.75rem;

          &.warning {
            color: #fbbf24;
          }
        }
      }
    }
  }
}

// Animations
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 30px rgba(20, 184, 166, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(20, 184, 166, 0.6);
    transform: scale(1.05);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  40% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .chat-header {
    padding: 1.25rem 1.5rem;

    .header-content {
      .header-left {
        .ai-avatar .avatar-glow {
          width: 45px;
          height: 45px;
          font-size: 1.1rem;
        }

        .header-text {
          .assistant-title {
            font-size: 1.3rem;
          }
        }
      }
    }
  }

  .messages-container {
    padding: 1.5rem;
  }

  .chat-input-section {
    padding: 1.25rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .ai-chat-container {
    height: calc(100vh - 100px);
  }

  .chat-header {
    padding: 1rem;

    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      .header-left {
        .ai-avatar .avatar-glow {
          width: 40px;
          height: 40px;
          font-size: 1rem;
        }

        .header-text {
          .assistant-title {
            font-size: 1.2rem;
          }

          .assistant-subtitle {
            font-size: 0.85rem;
          }
        }
      }

      .header-actions {
        align-self: stretch;
        justify-content: space-between;

        .action-btn {
          flex: 1;
          justify-content: center;
          padding: 0.6rem 1rem;
          font-size: 0.85rem;

          span {
            display: none;
          }
        }
      }
    }

    .conversation-status {
      margin-top: 0.75rem;

      .status-indicator {
        padding: 0.6rem 0.8rem;

        .conversation-title {
          display: none;
        }
      }
    }
  }

  .messages-container {
    padding: 1rem;
  }

  .welcome-section {
    .welcome-content {
      .welcome-title {
        font-size: 1.6rem;
      }

      .welcome-description {
        font-size: 1rem;
      }

      .quick-actions {
        .action-chips {
          .chip {
            font-size: 0.8rem;
            padding: 0.6rem 0.8rem;
          }
        }
      }
    }
  }

  .messages-list {
    .message-wrapper {
      &.user-message .message-bubble,
      &.ai-message .message-bubble {
        max-width: 90%;
      }

      &.ai-message .message-bubble {
        .message-avatar .ai-avatar-small {
          width: 32px;
          height: 32px;
          font-size: 0.8rem;
        }
      }
    }
  }

  .chat-input-section {
    padding: 1rem;

    .conversation-selector {
      .selector-wrapper {
        max-width: 100%;
      }
    }

    .input-container {
      .input-wrapper {
        padding: 0.6rem 0.8rem;

        .message-input {
          font-size: 0.9rem;
        }

        .input-actions {
          margin-left: 0.5rem;

          .send-button {
            width: 36px;
            height: 36px;
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  .error-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;

    .error-content {
      max-width: none;
    }
  }
}

@media (max-width: 480px) {
  .chat-header {
    .header-content {
      .header-actions {
        .action-btn {
          padding: 0.5rem 0.75rem;
          font-size: 0.8rem;

          i {
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  .welcome-section {
    .welcome-content {
      .welcome-title {
        font-size: 1.4rem;
      }

      .disclaimer-card {
        padding: 1rem;

        .disclaimer-icon {
          width: 35px;
          height: 35px;
        }
      }

      .quick-actions {
        .action-chips {
          .chip {
            font-size: 0.75rem;
            padding: 0.5rem 0.7rem;
          }
        }
      }
    }
  }

  .messages-list {
    .message-wrapper {
      margin-bottom: 1.5rem;

      .message-bubble {
        .message-content {
          padding: 0.8rem 1rem;
        }
      }
    }
  }
}

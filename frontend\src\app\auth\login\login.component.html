<div class="container-fluid vh-100">
  <div class="row h-100">
    <!-- Left side - Branding -->
    <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center bg-primary text-white">
      <div class="text-center">
        <div class="mb-4 d-flex justify-content-center">
          <div class="healthconnect-logo logo-xl">
            <div class="logo-icon">
              <i class="bi bi-heart-pulse"></i>
            </div>
          </div>
        </div>
        <h1 class="display-4 fw-bold mb-3">HealthConnect</h1>
        <p class="lead">Your trusted medical platform for seamless healthcare management</p>
        <div class="mt-4">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="bi bi-check-circle me-2"></i>
            <span>Secure Patient-Doctor Communication</span>
          </div>
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="bi bi-check-circle me-2"></i>
            <span>Easy Appointment Scheduling</span>
          </div>
          <div class="d-flex align-items-center justify-content-center">
            <i class="bi bi-check-circle me-2"></i>
            <span>AI-Powered Health Assistance</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Premium Login Form -->
    <div class="col-md-6 d-flex align-items-center justify-content-center p-4">
      <div class="w-100" style="max-width: 450px;">
        <div class="login-form-container">
          <div class="text-center mb-4">
            <div class="mb-3 d-flex justify-content-center">
              <div class="healthconnect-logo logo-md">
                <div class="logo-icon">
                  <i class="bi bi-heart-pulse"></i>
                </div>
              </div>
            </div>
            <h2 class="fw-bold text-dark mb-2">Welcome Back</h2>
            <p class="text-muted">Sign in to your HealthConnect account</p>
          </div>

        <!-- Error Alert -->
        <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          {{ errorMessage }}
        </div>

        <!-- Loading State -->
        <div *ngIf="!loginForm" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

          <!-- Premium Login Form -->
          <form *ngIf="loginForm" [formGroup]="loginForm" (ngSubmit)="onSubmit()" novalidate>
            <!-- Email Field -->
            <div class="mb-3">
              <label for="email" class="form-label">
                <i class="bi bi-envelope me-2"></i>Email Address
              </label>
              <input
                type="email"
                class="form-control"
                id="email"
                formControlName="email"
                placeholder="Enter your email address"
                [class.is-invalid]="isFieldInvalid('email')"
              >
              <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
                {{ getFieldError('email') }}
              </div>
            </div>

            <!-- Password Field -->
            <div class="mb-3">
              <label for="password" class="form-label">
                <i class="bi bi-lock me-2"></i>Password
              </label>
              <input
                type="password"
                class="form-control"
                id="password"
                formControlName="password"
                placeholder="Enter your password"
                [class.is-invalid]="isFieldInvalid('password')"
              >
              <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
                {{ getFieldError('password') }}
              </div>
            </div>

            <!-- Remember Me -->
            <div class="mb-4 form-check d-flex align-items-center">
              <input
                type="checkbox"
                class="form-check-input me-2"
                id="rememberMe"
                formControlName="rememberMe"
                style="transform: scale(1.2);"
              >
              <label class="form-check-label" for="rememberMe" style="color: var(--hc-gray-600); font-weight: 500;">
                Remember me for 30 days
              </label>
            </div>

            <!-- Premium Submit Button -->
            <button
              type="submit"
              class="btn btn-premium w-100 mb-3"
              [disabled]="isLoading"
            >
              <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <span *ngIf="isLoading">Signing In...</span>
              <span *ngIf="!isLoading">
                <i class="bi bi-arrow-right-circle me-2"></i>Sign In Securely
              </span>
            </button>
          </form>

          <!-- Register Link -->
          <div class="text-center">
            <p class="text-muted mb-0">
              Don't have an account?
              <a routerLink="/auth/register" class="text-primary text-decoration-none fw-semibold ms-1"
                 style="color: #0d9488 !important;">
                Create Account
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

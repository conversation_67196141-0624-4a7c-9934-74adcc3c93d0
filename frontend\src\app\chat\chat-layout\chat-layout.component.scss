.chat-layout {
  display: flex;
  height: calc(100vh - 120px); // Adjust based on your header height
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-sidebar {
  width: 350px;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.chat-sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h5 {
    color: #495057;
    font-weight: 600;
  }

  .btn {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
}

.chat-sidebar-body {
  flex: 1;
  overflow-y: auto;
}

.chat-items {
  padding: 0.5rem 0;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f1f3f4;

  &:hover {
    background-color: #e9ecef;
  }

  &.active {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
  }
}

.chat-avatar {
  position: relative;
  margin-right: 0.75rem;

  .avatar-img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
  }

  .online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #4caf50;
    border: 2px solid #fff;
    border-radius: 50%;
  }
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.chat-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #212529;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-time {
  font-size: 0.75rem;
  white-space: nowrap;
}

.chat-preview {
  p {
    font-size: 0.8rem;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.chat-meta {
  margin-left: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;

  .badge {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// New Chat Modal Styles
.modal {
  &.show {
    background: rgba(0, 0, 0, 0.5);
  }
}

.contact-list, .doctor-list {
  max-height: 400px;
  overflow-y: auto;
}

.contact-item, .doctor-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f8f9fa;
    border-color: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.contact-avatar, .doctor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
}

.contact-info, .doctor-info {
  flex: 1;

  h6 {
    margin-bottom: 0.25rem;
    color: #212529;
    font-weight: 600;
  }

  .text-muted {
    font-size: 0.85rem;
  }

  .contact-details, .doctor-details {
    margin-top: 0.25rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .chat-layout {
    height: calc(100vh - 80px);
  }

  .chat-sidebar {
    width: 100%;
    position: absolute;
    z-index: 10;
    height: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s;

    &.show {
      transform: translateX(0);
    }
  }

  .chat-main {
    width: 100%;
  }
}

// Loading and Empty States
.text-center {
  .spinner-border-sm {
    width: 1.5rem;
    height: 1.5rem;
  }

  .fs-1 {
    font-size: 3rem !important;
    opacity: 0.3;
  }
}

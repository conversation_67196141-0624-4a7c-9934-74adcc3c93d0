// Doctor dashboard specific styles

.appointment-item {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.appointment-item:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.activity-item {
  padding-bottom: 1rem;
}

.activity-item:last-child {
  padding-bottom: 0;
}

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

.btn-outline-primary,
.btn-outline-success,
.btn-outline-info,
.btn-outline-warning {
  border-width: 2px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

.text-primary {
  color: #0d6efd !important;
}

.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .appointment-item {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .appointment-item .flex-shrink-0:last-child {
    margin-top: 1rem;
    align-self: stretch;
  }
  
  .appointment-item .btn {
    width: 100%;
  }
}

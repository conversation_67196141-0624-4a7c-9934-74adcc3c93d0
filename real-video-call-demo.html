<!DOCTYPE html>
<html>
<head>
    <title>🎥 HealthConnect Real Video Call Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7/bundles/stomp.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/simple-peer@9.11.1/simplepeer.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .demo-section { 
            display: flex; 
            gap: 20px; 
            margin: 20px 0; 
        }
        .user-panel { 
            flex: 1; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            padding: 15px; 
            background: #fafafa;
        }
        .patient-panel { border-color: #4CAF50; }
        .doctor-panel { border-color: #2196F3; }
        .video-container { 
            display: flex; 
            gap: 20px; 
            margin: 20px 0; 
        }
        .video-panel { 
            flex: 1; 
            border: 2px solid #333; 
            border-radius: 10px; 
            background: #000; 
            min-height: 400px; 
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        video { 
            width: 100%; 
            height: 100%; 
            border-radius: 8px; 
            object-fit: cover;
        }
        .controls { 
            display: flex; 
            gap: 10px; 
            margin: 10px 0; 
            flex-wrap: wrap;
        }
        button { 
            padding: 10px 15px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-weight: bold;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .status { 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .log { 
            height: 300px; 
            overflow-y: scroll; 
            border: 1px solid #ddd; 
            padding: 10px; 
            background: #f9f9f9; 
            font-family: monospace; 
            font-size: 12px;
        }
        .info-panel {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .user-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connection-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .connected { background: #4CAF50; }
        .connecting { background: #ff9800; }
        .disconnected { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 HealthConnect Real Video Call Demo</h1>
        
        <div class="info-panel">
            <h3>📋 Real Video Calling Demonstration</h3>
            <p>This demo shows the actual video calling feature working with real peer-to-peer WebRTC connections.</p>
            <p><strong>Test Users:</strong></p>
            <ul>
                <li><strong>Patient:</strong> Emma Wilson (<EMAIL>) / demo123</li>
                <li><strong>Doctor:</strong> Dr. Michael Chen (<EMAIL>) / demo123</li>
            </ul>
            <p><strong>Room ID:</strong> room_ce48f8a711314d2b9e28dd07b931c5a3</p>
            <p><strong>Instructions:</strong> Login both users, then start video call to see real peer-to-peer video streaming!</p>
        </div>

        <div class="demo-section">
            <!-- Patient Panel -->
            <div class="user-panel patient-panel">
                <h3>👤 Patient: Emma Wilson</h3>
                <div class="user-info" id="patient-info">
                    <span class="connection-indicator disconnected" id="patient-indicator"></span>
                    Not logged in
                </div>
                <div class="controls">
                    <button class="btn-primary" onclick="loginPatient()">Login</button>
                    <button class="btn-success" onclick="startPatientVideo()" id="patient-start" disabled>Start Video</button>
                    <button class="btn-danger" onclick="stopPatientVideo()" id="patient-stop" disabled>Stop Video</button>
                </div>
                <div class="status" id="patient-status">Ready to login</div>
            </div>

            <!-- Doctor Panel -->
            <div class="user-panel doctor-panel">
                <h3>👨‍⚕️ Doctor: Dr. Michael Chen</h3>
                <div class="user-info" id="doctor-info">
                    <span class="connection-indicator disconnected" id="doctor-indicator"></span>
                    Not logged in
                </div>
                <div class="controls">
                    <button class="btn-primary" onclick="loginDoctor()">Login</button>
                    <button class="btn-success" onclick="startDoctorVideo()" id="doctor-start" disabled>Start Video</button>
                    <button class="btn-danger" onclick="stopDoctorVideo()" id="doctor-stop" disabled>Stop Video</button>
                </div>
                <div class="status" id="doctor-status">Ready to login</div>
            </div>
        </div>

        <!-- Video Streams -->
        <div class="video-container">
            <div class="video-panel">
                <video id="patient-video" autoplay muted playsinline style="display: none;"></video>
                <div id="patient-placeholder">Patient Video (Not Connected)</div>
            </div>
            <div class="video-panel">
                <video id="doctor-video" autoplay playsinline style="display: none;"></video>
                <div id="doctor-placeholder">Doctor Video (Not Connected)</div>
            </div>
        </div>

        <!-- WebRTC Controls -->
        <div class="controls">
            <button class="btn-warning" onclick="establishPeerConnection()">Establish P2P Connection</button>
            <button class="btn-primary" onclick="testSignaling()">Test Signaling</button>
            <button class="btn-danger" onclick="clearConnections()">Clear Connections</button>
            <button class="btn-primary" onclick="clearLog()">Clear Log</button>
        </div>

        <!-- Real App Integration -->
        <div class="controls">
            <button class="btn-success" onclick="openRealConsultationRoom()">🚀 Open Real Consultation Room</button>
            <button class="btn-primary" onclick="openDoctorLogin()">👨‍⚕️ Doctor Login</button>
            <button class="btn-primary" onclick="openPatientLogin()">👤 Patient Login</button>
        </div>

        <!-- Activity Log -->
        <h3>📊 Real-Time Activity Log</h3>
        <div class="log" id="activity-log"></div>
    </div>

    <script>
        // Demo configuration
        const CONFIG = {
            API_BASE: 'http://localhost:8080',
            WS_URL: 'http://localhost:8080/api/ws',
            FRONTEND_URL: 'http://localhost:4200',
            ROOM_ID: 'room_ce48f8a711314d2b9e28dd07b931c5a3',
            PATIENT: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Emma Wilson',
                id: 11
            },
            DOCTOR: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Dr. Michael Chen',
                id: 12
            }
        };

        // Global state
        let patientToken = null;
        let doctorToken = null;
        let patientStompClient = null;
        let doctorStompClient = null;
        let patientStream = null;
        let doctorStream = null;
        let patientPeer = null;
        let doctorPeer = null;

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red' : type === 'success' ? 'color: green' : 'color: blue';
            logDiv.innerHTML += `<div style="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateIndicator(indicatorId, status) {
            const indicator = document.getElementById(indicatorId);
            indicator.className = `connection-indicator ${status}`;
        }

        // Authentication functions
        async function loginPatient() {
            try {
                log('🔐 Patient login attempt...', 'info');
                updateStatus('patient-status', 'Logging in...', 'connecting');
                updateIndicator('patient-indicator', 'connecting');

                const response = await fetch(`${CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: CONFIG.PATIENT.email,
                        password: CONFIG.PATIENT.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    document.getElementById('patient-info').innerHTML = `<span class="connection-indicator connected" id="patient-indicator"></span>✅ Logged in as ${CONFIG.PATIENT.name}`;
                    document.getElementById('patient-start').disabled = false;
                    updateStatus('patient-status', 'Logged in successfully', 'connected');
                    updateIndicator('patient-indicator', 'connected');
                    log('✅ Patient login successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Patient login failed: ${error.message}`, 'error');
                updateStatus('patient-status', 'Login failed', 'error');
                updateIndicator('patient-indicator', 'disconnected');
            }
        }

        async function loginDoctor() {
            try {
                log('🔐 Doctor login attempt...', 'info');
                updateStatus('doctor-status', 'Logging in...', 'connecting');
                updateIndicator('doctor-indicator', 'connecting');

                const response = await fetch(`${CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: CONFIG.DOCTOR.email,
                        password: CONFIG.DOCTOR.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    document.getElementById('doctor-info').innerHTML = `<span class="connection-indicator connected" id="doctor-indicator"></span>✅ Logged in as ${CONFIG.DOCTOR.name}`;
                    document.getElementById('doctor-start').disabled = false;
                    updateStatus('doctor-status', 'Logged in successfully', 'connected');
                    updateIndicator('doctor-indicator', 'connected');
                    log('✅ Doctor login successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Doctor login failed: ${error.message}`, 'error');
                updateStatus('doctor-status', 'Login failed', 'error');
                updateIndicator('doctor-indicator', 'disconnected');
            }
        }

        // Video functions
        async function startPatientVideo() {
            try {
                log('🎥 Starting patient video...', 'info');
                updateStatus('patient-status', 'Starting video...', 'connecting');

                // Get user media
                patientStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });

                const videoElement = document.getElementById('patient-video');
                const placeholder = document.getElementById('patient-placeholder');
                
                videoElement.srcObject = patientStream;
                videoElement.style.display = 'block';
                placeholder.style.display = 'none';

                // Connect WebSocket
                await connectPatientWebSocket();

                document.getElementById('patient-start').disabled = true;
                document.getElementById('patient-stop').disabled = false;
                updateStatus('patient-status', 'Video active', 'connected');
                log('✅ Patient video started successfully', 'success');

            } catch (error) {
                log(`❌ Patient video failed: ${error.message}`, 'error');
                updateStatus('patient-status', 'Video failed', 'error');
            }
        }

        async function startDoctorVideo() {
            try {
                log('🎥 Starting doctor video...', 'info');
                updateStatus('doctor-status', 'Starting video...', 'connecting');

                // Connect WebSocket (doctor doesn't need camera for this demo)
                await connectDoctorWebSocket();

                document.getElementById('doctor-start').disabled = true;
                document.getElementById('doctor-stop').disabled = false;
                updateStatus('doctor-status', 'Connected to call', 'connected');
                log('✅ Doctor connected to video call', 'success');

            } catch (error) {
                log(`❌ Doctor video failed: ${error.message}`, 'error');
                updateStatus('doctor-status', 'Video failed', 'error');
            }
        }

        // WebSocket connections
        async function connectPatientWebSocket() {
            return new Promise((resolve, reject) => {
                patientStompClient = new StompJs.Client({
                    webSocketFactory: () => new SockJS(CONFIG.WS_URL),
                    connectHeaders: {
                        Authorization: `Bearer ${patientToken}`
                    },
                    debug: (str) => {
                        log(`Patient STOMP: ${str}`, 'info');
                    },
                    onConnect: (frame) => {
                        log('✅ Patient WebSocket connected', 'success');
                        
                        // Subscribe to WebRTC room
                        patientStompClient.subscribe(`/topic/webrtc/${CONFIG.ROOM_ID}`, (message) => {
                            const signal = JSON.parse(message.body);
                            log(`📡 Patient received: ${signal.type}`, 'info');
                            handlePatientSignal(signal);
                        });

                        // Send join message
                        patientStompClient.publish({
                            destination: `/app/webrtc/${CONFIG.ROOM_ID}/join`,
                            body: JSON.stringify({
                                userId: CONFIG.PATIENT.id,
                                userRole: 'PATIENT'
                            }),
                            headers: {
                                Authorization: `Bearer ${patientToken}`
                            }
                        });

                        resolve();
                    },
                    onStompError: (frame) => {
                        log(`❌ Patient STOMP Error: ${frame.body}`, 'error');
                        reject(new Error('STOMP connection failed'));
                    }
                });

                patientStompClient.activate();
            });
        }

        async function connectDoctorWebSocket() {
            return new Promise((resolve, reject) => {
                doctorStompClient = new StompJs.Client({
                    webSocketFactory: () => new SockJS(CONFIG.WS_URL),
                    connectHeaders: {
                        Authorization: `Bearer ${doctorToken}`
                    },
                    debug: (str) => {
                        log(`Doctor STOMP: ${str}`, 'info');
                    },
                    onConnect: (frame) => {
                        log('✅ Doctor WebSocket connected', 'success');
                        
                        // Subscribe to WebRTC room
                        doctorStompClient.subscribe(`/topic/webrtc/${CONFIG.ROOM_ID}`, (message) => {
                            const signal = JSON.parse(message.body);
                            log(`📡 Doctor received: ${signal.type}`, 'info');
                            handleDoctorSignal(signal);
                        });

                        // Send join message
                        doctorStompClient.publish({
                            destination: `/app/webrtc/${CONFIG.ROOM_ID}/join`,
                            body: JSON.stringify({
                                userId: CONFIG.DOCTOR.id,
                                userRole: 'DOCTOR'
                            }),
                            headers: {
                                Authorization: `Bearer ${doctorToken}`
                            }
                        });

                        resolve();
                    },
                    onStompError: (frame) => {
                        log(`❌ Doctor STOMP Error: ${frame.body}`, 'error');
                        reject(new Error('STOMP connection failed'));
                    }
                });

                doctorStompClient.activate();
            });
        }

        // Signal handlers
        function handlePatientSignal(signal) {
            if (signal.type === 'USER_JOINED' && signal.data.userRole === 'DOCTOR') {
                log('👨‍⚕️ Doctor joined - Patient can initiate peer connection', 'success');
            }
        }

        function handleDoctorSignal(signal) {
            if (signal.type === 'USER_JOINED' && signal.data.userRole === 'PATIENT') {
                log('👤 Patient joined - Doctor can receive peer connection', 'success');
            }
        }

        // WebRTC Peer Connection
        function establishPeerConnection() {
            if (!patientStream) {
                log('❌ Patient video must be started first', 'error');
                return;
            }

            if (!doctorStompClient || !doctorStompClient.connected) {
                log('❌ Doctor must be connected first', 'error');
                return;
            }

            try {
                log('🔗 Establishing peer-to-peer connection...', 'info');

                // Create peer connection for patient (initiator)
                patientPeer = new SimplePeer({
                    initiator: true,
                    stream: patientStream,
                    config: {
                        iceServers: [
                            { urls: 'stun:stun.l.google.com:19302' },
                            { urls: 'stun:stun1.l.google.com:19302' }
                        ]
                    }
                });

                // Create peer connection for doctor (receiver)
                doctorPeer = new SimplePeer({
                    initiator: false,
                    config: {
                        iceServers: [
                            { urls: 'stun:stun.l.google.com:19302' },
                            { urls: 'stun:stun1.l.google.com:19302' }
                        ]
                    }
                });

                // Patient peer events
                patientPeer.on('signal', (data) => {
                    log('📤 Patient sending signal to doctor', 'info');
                    // In real implementation, this would go through WebSocket
                    doctorPeer.signal(data);
                });

                patientPeer.on('connect', () => {
                    log('✅ Patient peer connected', 'success');
                });

                patientPeer.on('error', (err) => {
                    log(`❌ Patient peer error: ${err}`, 'error');
                });

                // Doctor peer events
                doctorPeer.on('signal', (data) => {
                    log('📤 Doctor sending signal to patient', 'info');
                    // In real implementation, this would go through WebSocket
                    patientPeer.signal(data);
                });

                doctorPeer.on('stream', (stream) => {
                    log('✅ Doctor receiving patient video stream', 'success');
                    const videoElement = document.getElementById('doctor-video');
                    const placeholder = document.getElementById('doctor-placeholder');
                    
                    videoElement.srcObject = stream;
                    videoElement.style.display = 'block';
                    placeholder.style.display = 'none';
                });

                doctorPeer.on('connect', () => {
                    log('✅ Doctor peer connected', 'success');
                });

                doctorPeer.on('error', (err) => {
                    log(`❌ Doctor peer error: ${err}`, 'error');
                });

                log('🎯 Peer connection establishment initiated', 'success');

            } catch (error) {
                log(`❌ Peer connection failed: ${error.message}`, 'error');
            }
        }

        function testSignaling() {
            if (patientStompClient && patientStompClient.connected) {
                patientStompClient.publish({
                    destination: `/app/webrtc/${CONFIG.ROOM_ID}/signal`,
                    body: JSON.stringify({
                        type: 'test-signal',
                        userId: CONFIG.PATIENT.id,
                        data: 'Test from patient'
                    }),
                    headers: {
                        Authorization: `Bearer ${patientToken}`
                    }
                });
                log('📤 Test signal sent from patient', 'success');
            }

            if (doctorStompClient && doctorStompClient.connected) {
                doctorStompClient.publish({
                    destination: `/app/webrtc/${CONFIG.ROOM_ID}/signal`,
                    body: JSON.stringify({
                        type: 'test-signal',
                        userId: CONFIG.DOCTOR.id,
                        data: 'Test from doctor'
                    }),
                    headers: {
                        Authorization: `Bearer ${doctorToken}`
                    }
                });
                log('📤 Test signal sent from doctor', 'success');
            }
        }

        function clearConnections() {
            if (patientPeer) {
                patientPeer.destroy();
                patientPeer = null;
            }
            if (doctorPeer) {
                doctorPeer.destroy();
                doctorPeer = null;
            }
            log('🔌 Peer connections cleared', 'info');
        }

        function stopPatientVideo() {
            if (patientStream) {
                patientStream.getTracks().forEach(track => track.stop());
                patientStream = null;
            }
            if (patientStompClient) {
                patientStompClient.deactivate();
                patientStompClient = null;
            }
            document.getElementById('patient-video').style.display = 'none';
            document.getElementById('patient-placeholder').style.display = 'block';
            document.getElementById('patient-start').disabled = false;
            document.getElementById('patient-stop').disabled = true;
            updateStatus('patient-status', 'Video stopped', 'info');
            log('🔌 Patient video stopped', 'info');
        }

        function stopDoctorVideo() {
            if (doctorStompClient) {
                doctorStompClient.deactivate();
                doctorStompClient = null;
            }
            document.getElementById('doctor-video').style.display = 'none';
            document.getElementById('doctor-placeholder').style.display = 'block';
            document.getElementById('doctor-start').disabled = false;
            document.getElementById('doctor-stop').disabled = true;
            updateStatus('doctor-status', 'Disconnected', 'info');
            log('🔌 Doctor disconnected', 'info');
        }

        // Real app integration
        function openRealConsultationRoom() {
            const url = `${CONFIG.FRONTEND_URL}/telemedicine/consultation-room/${CONFIG.ROOM_ID}`;
            log(`🚀 Opening real consultation room: ${url}`, 'info');
            window.open(url, '_blank');
        }

        function openDoctorLogin() {
            const url = `${CONFIG.FRONTEND_URL}/auth/login`;
            log(`👨‍⚕️ Opening doctor login`, 'info');
            window.open(url, '_blank');
        }

        function openPatientLogin() {
            const url = `${CONFIG.FRONTEND_URL}/auth/login`;
            log(`👤 Opening patient login`, 'info');
            window.open(url, '_blank');
        }

        // Initialize
        log('🚀 Real Video Call Demo initialized', 'info');
        log('📋 Login both users, start patient video, then establish peer connection to see real video streaming!', 'info');
    </script>
</body>
</html>

import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { VideoConsultationService, VideoConsultation } from '../../core/services/video-consultation.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { ChatService } from '../../core/services/chat.service';
import { PresenceService } from '../../core/services/presence.service';
import { AuthService } from '../../core/services/auth.service';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-consultation-room',
  templateUrl: './consultation-room.component.html',
  styleUrls: ['./consultation-room.component.scss']
})
export class ConsultationRoomComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('localVideo', { static: false }) localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo', { static: false }) remoteVideo!: ElementRef<HTMLVideoElement>;

  consultation: VideoConsultation | null = null;
  roomId: string = '';
  currentUser: any;
  
  // Video call state
  isVideoEnabled = true;
  isAudioEnabled = true;
  isScreenSharing = false;
  isCallActive = false;
  isConnecting = false;
  
  // UI state
  isChatOpen = false;
  isControlsVisible = true;
  showParticipants = false;
  
  // Chat
  messages: any[] = [];
  newMessage = '';
  
  // Error handling
  error: string | null = null;
  connectionStatus = 'Connecting...';

  // Agora Video properties
  private agoraClient: any = null;
  private localAudioTrack: any = null;
  private localVideoTrack: any = null;
  private remoteUsers: Map<string, any> = new Map();
  private APP_ID = environment.agora.appId;

  // Call duration tracking
  callDuration = 0;
  private callStartTime: Date | null = null;
  private durationInterval: any;

  // Recording functionality
  isRecording = false;
  private recordingStartTime: Date | null = null;

  private subscriptions: Subscription[] = [];
  private controlsTimeout: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private videoConsultationService: VideoConsultationService,
    private http: HttpClient,
    private chatService: ChatService,
    private presenceService: PresenceService,
    private authService: AuthService,
    private notificationService: NotificationService
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.roomId = params['roomId'];
      console.log('[DEBUG] ngOnInit roomId param:', this.roomId);
      if (this.roomId) {
        this.initializeConsultation();
      }
    });

    // Auto-hide controls after 5 seconds of inactivity
    this.resetControlsTimeout();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.leaveAgoraCall();
    this.stopCallDurationTracking();
    if (this.controlsTimeout) {
      clearTimeout(this.controlsTimeout);
    }
  }

  public async initializeConsultation(): Promise<void> {
    try {
      this.isConnecting = true;
      this.connectionStatus = 'Loading consultation...';
      console.log('[DEBUG] initializeConsultation using roomId:', this.roomId);
      // Get consultation details
      const sub = this.videoConsultationService.getConsultationByRoomId(this.roomId).subscribe({
        next: (consultation) => {
          this.consultation = consultation;
          this.continueInitialization();
        },
        error: (error) => {
          console.error('Failed to get consultation:', error);
          this.error = 'Failed to load consultation details';
          this.isConnecting = false;
        }
      });
      this.subscriptions.push(sub);
    } catch (error) {
      console.error('Failed to initialize consultation:', error);
      this.error = 'Failed to join the consultation. Please try again.';
      this.isConnecting = false;
    }
  }

  private async continueInitialization(): Promise<void> {
    try {
      // Check if user is authorized
      if (!this.isAuthorizedUser()) {
        this.error = 'You are not authorized to join this consultation';
        return;
      }

      // Initialize Agora Video
      this.connectionStatus = 'Connecting to video call...';
      await this.initializeAgoraVideo();

      this.isConnecting = false;
      this.isCallActive = true;
      this.connectionStatus = 'Connected';

      // Start call duration tracking
      this.startCallDurationTracking();
    } catch (error) {
      console.error('Failed to continue initialization:', error);
      this.error = 'Failed to initialize video consultation';
      this.isConnecting = false;
    }
  }

  private async initializeAgoraVideo(): Promise<void> {
    try {
      console.log('Starting Agora Video initialization...');

      // Set user as busy during video call
      this.presenceService.updatePresence('BUSY', 'In video consultation');

      // Load Agora SDK dynamically
      await this.loadAgoraSDK();

      // Initialize Agora client with modern API
      this.agoraClient = (window as any).AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });

      // Setup event handlers before joining
      this.setupAgoraEventHandlers();

      // Get Agora token from backend (for demo, we'll use null)
      const token = null; // In production: await this.getAgoraToken();
      const channelName = this.roomId;
      const uid = this.currentUser.id;

      this.connectionStatus = 'Joining video channel...';

      // Join Agora channel
      await this.agoraClient.join(this.APP_ID, channelName, token, uid);
      this.connectionStatus = 'Connected! Getting camera and microphone...';

      // Create local audio and video tracks
      try {
        [this.localAudioTrack, this.localVideoTrack] = await (window as any).AgoraRTC.createMicrophoneAndCameraTracks();

        this.connectionStatus = 'Camera and microphone ready! Publishing stream...';

        // Play local video track
        if (this.localVideo) {
          this.localVideoTrack.play(this.localVideo.nativeElement);
        }

        // Publish local tracks
        await this.agoraClient.publish([this.localAudioTrack, this.localVideoTrack]);
        this.connectionStatus = '✅ Connected and ready for video call!';

        // Start call duration tracking
        this.startCallDurationTracking();

        console.log('Agora Video initialized successfully');

      } catch (mediaError) {
        console.error('Failed to get media devices:', mediaError);
        this.connectionStatus = 'Failed to access camera/microphone';
        this.error = 'Please allow camera and microphone access to join the video call.';
        throw mediaError;
      }

    } catch (error) {
      console.error('Failed to initialize Agora video:', error);
      this.connectionStatus = 'Failed to connect to video call';
      this.presenceService.updatePresence('ONLINE');
      this.error = 'Failed to initialize video call. Please check your camera and microphone permissions.';
      throw new Error('Failed to initialize video call');
    }
  }

  private async loadAgoraSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if ((window as any).AgoraRTC) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://download.agora.io/sdk/release/AgoraRTC_N-4.20.2.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Agora SDK'));
      document.head.appendChild(script);
    });
  }

  private setupAgoraEventHandlers(): void {
    // Handle user published (remote user joins and publishes)
    this.agoraClient.on('user-published', async (user: any, mediaType: string) => {
      console.log('Remote user published:', user.uid, mediaType);

      // Subscribe to the remote user
      await this.agoraClient.subscribe(user, mediaType);

      // Store remote user
      this.remoteUsers.set(user.uid, user);

      if (mediaType === 'video') {
        console.log('Playing remote video');
        this.connectionStatus = '✅ Connected with remote participant!';

        // Play remote video track
        if (this.remoteVideo && user.videoTrack) {
          user.videoTrack.play(this.remoteVideo.nativeElement);
        }
      }

      if (mediaType === 'audio') {
        console.log('Playing remote audio');
        // Audio will play automatically
        if (user.audioTrack) {
          user.audioTrack.play();
        }
      }
    });

    // Handle user unpublished
    this.agoraClient.on('user-unpublished', (user: any, mediaType: string) => {
      console.log('Remote user unpublished:', user.uid, mediaType);

      if (mediaType === 'video' && this.remoteVideo) {
        // Stop playing video
        this.remoteVideo.nativeElement.srcObject = null;
      }
    });

    // Handle user left
    this.agoraClient.on('user-left', (user: any) => {
      console.log('Remote user left:', user.uid);
      this.remoteUsers.delete(user.uid);
      this.connectionStatus = 'Remote participant left the call';

      // Clear remote video
      if (this.remoteVideo) {
        this.remoteVideo.nativeElement.srcObject = null;
      }
    });

    // Handle connection state changes
    this.agoraClient.on('connection-state-change', (curState: string, revState: string) => {
      console.log('Connection state changed:', curState, 'from', revState);
      if (curState === 'DISCONNECTED') {
        this.connectionStatus = 'Connection lost. Attempting to reconnect...';
      }
    });
  }

  private async leaveAgoraCall(): Promise<void> {
    try {
      // Close local tracks
      if (this.localAudioTrack) {
        this.localAudioTrack.close();
        this.localAudioTrack = null;
      }

      if (this.localVideoTrack) {
        this.localVideoTrack.close();
        this.localVideoTrack = null;
      }

      // Leave the channel
      if (this.agoraClient) {
        await this.agoraClient.leave();
        this.agoraClient = null;
      }

      // Clear remote users
      this.remoteUsers.clear();

      console.log('Left Agora call successfully');
    } catch (error) {
      console.error('Error leaving Agora call:', error);
    }
  }

  // Event listeners are now handled in setupAgoraEventHandlers method

  private isAuthorizedUser(): boolean {
    if (!this.consultation) return false;
    
    return this.consultation.doctor.id === this.currentUser.id || 
           this.consultation.patient.id === this.currentUser.id;
  }

  private handleConnectionError(): void {
    this.error = 'Connection lost. Attempting to reconnect...';
    // Implement reconnection logic here
  }

  // Video controls
  async toggleVideo(): Promise<void> {
    try {
      if (!this.localVideoTrack) return;

      if (this.isVideoEnabled) {
        await this.localVideoTrack.setEnabled(false);
      } else {
        await this.localVideoTrack.setEnabled(true);
      }

      this.isVideoEnabled = !this.isVideoEnabled;
      this.resetControlsTimeout();
    } catch (error) {
      console.error('Failed to toggle video:', error);
    }
  }

  async toggleAudio(): Promise<void> {
    try {
      if (!this.localAudioTrack) return;

      if (this.isAudioEnabled) {
        await this.localAudioTrack.setEnabled(false);
      } else {
        await this.localAudioTrack.setEnabled(true);
      }

      this.isAudioEnabled = !this.isAudioEnabled;
      this.resetControlsTimeout();
    } catch (error) {
      console.error('Failed to toggle audio:', error);
    }
  }

  async toggleScreenShare(): Promise<void> {
    try {
      // Note: Screen sharing with Agora Video requires additional implementation
      // For now, we'll show a placeholder message
      this.notificationService.addNotification({
        type: 'system',
        title: 'Screen Share',
        message: 'Screen sharing feature will be available in the next update',
        priority: 'medium'
      });
      this.resetControlsTimeout();
    } catch (error) {
      console.error('Screen share toggle failed:', error);
      this.notificationService.addNotification({
        type: 'system',
        title: 'Screen Share Error',
        message: 'Failed to toggle screen sharing',
        priority: 'medium'
      });
    }
  }

  // UI controls
  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadChatMessages();
    }
  }

  toggleParticipants(): void {
    this.showParticipants = !this.showParticipants;
  }

  onMouseMove(): void {
    this.isControlsVisible = true;
    this.resetControlsTimeout();
  }

  private resetControlsTimeout(): void {
    if (this.controlsTimeout) {
      clearTimeout(this.controlsTimeout);
    }
    this.controlsTimeout = setTimeout(() => {
      this.isControlsVisible = false;
    }, 5000);
  }

  // Chat functionality
  private loadChatMessages(): void {
    if (!this.consultation?.appointment?.id) return;

    // For now, initialize empty messages array
    // In a full implementation, this would load appointment-specific chat
    this.messages = [];

    // Subscribe to new messages
    const sub = this.chatService.messages$.subscribe({
      next: (message: any) => {
        this.messages.push(message);
      },
      error: (error: any) => {
        console.error('Failed to load chat messages:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  sendMessage(): void {
    if (!this.newMessage.trim()) return;

    // For video consultation chat, we'll use a simple approach
    // In a full implementation, this would integrate with the chat system
    const message = {
      id: Date.now(),
      content: this.newMessage.trim(),
      sender: this.currentUser,
      timestamp: new Date().toISOString(),
      status: 'SENT'
    };

    this.messages.push(message);
    this.newMessage = '';

    // In a real implementation, this would send via WebSocket or HTTP
    console.log('Video consultation message sent:', message);
  }

  // End consultation
  async endConsultation(): Promise<void> {
    if (this.currentUser.role !== 'DOCTOR') {
      this.leaveConsultation();
      return;
    }

    const confirmed = confirm('Are you sure you want to end this consultation?');
    if (!confirmed) return;

    try {
      // End Agora session
      this.isCallActive = false;
      await this.leaveAgoraCall();
      this.stopCallDurationTracking();

      // Reset presence to online
      this.presenceService.updatePresence('ONLINE');

      if (this.consultation?.id) {
        const sub = this.videoConsultationService.endConsultation(
          this.consultation.id,
          '', // notes - could be collected via modal
          '', // diagnosis
          ''  // recommendations
        ).subscribe({
          next: () => {
            this.notificationService.addNotification({
              type: 'system',
              title: 'Consultation Ended',
              message: 'The consultation has been ended successfully.',
              priority: 'medium'
            });
            this.router.navigate(['/telemedicine/consultations']);
          },
          error: (error) => {
            console.error('Failed to end consultation:', error);
            this.notificationService.addNotification({
              type: 'system',
              title: 'Error',
              message: 'Failed to end the consultation.',
              priority: 'high'
            });
          }
        });
        this.subscriptions.push(sub);
      }
    } catch (error) {
      console.error('Failed to end consultation:', error);
    }
  }

  leaveConsultation(): void {
    const confirmed = confirm('Are you sure you want to leave this consultation?');
    if (confirmed) {
      // End Agora session
      this.isCallActive = false;
      this.leaveAgoraCall();

      // Reset presence to online
      this.presenceService.updatePresence('ONLINE');

      this.router.navigate(['/telemedicine/consultations']);
    }
  }

  formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString();
  }

  // Call duration tracking
  private startCallDurationTracking(): void {
    this.callStartTime = new Date();
    this.callDuration = 0;

    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        this.callDuration = Math.floor((new Date().getTime() - this.callStartTime.getTime()) / 1000);
      }
    }, 1000);
  }

  private stopCallDurationTracking(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }

  formatDateTime(dateTime: string): string {
    return new Date(dateTime).toLocaleString();
  }

  getStatusColor(): string {
    if (!this.consultation) return 'secondary';

    switch (this.consultation.status) {
      case 'SCHEDULED': return 'primary';
      case 'WAITING_FOR_DOCTOR':
      case 'WAITING_FOR_PATIENT': return 'warning';
      case 'IN_PROGRESS': return 'success';
      case 'COMPLETED': return 'info';
      case 'CANCELLED':
      case 'NO_SHOW': return 'danger';
      default: return 'secondary';
    }
  }

  getStatusLabel(): string {
    if (!this.consultation) return 'Unknown';

    switch (this.consultation.status) {
      case 'SCHEDULED': return 'Scheduled';
      case 'WAITING_FOR_DOCTOR': return 'Waiting for Doctor';
      case 'WAITING_FOR_PATIENT': return 'Waiting for Patient';
      case 'IN_PROGRESS': return 'In Progress';
      case 'COMPLETED': return 'Completed';
      case 'CANCELLED': return 'Cancelled';
      case 'NO_SHOW': return 'No Show';
      default: return this.consultation.status;
    }
  }

  // Recording functionality
  formatRecordingDuration(): string {
    if (!this.recordingStartTime) return '00:00';

    const now = new Date();
    const duration = Math.floor((now.getTime() - this.recordingStartTime.getTime()) / 1000);
    return this.formatDuration(duration);
  }

  toggleRecording(): void {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording();
    }
  }

  private startRecording(): void {
    this.isRecording = true;
    this.recordingStartTime = new Date();
    // In a real implementation, this would start actual recording
    console.log('Recording started');
  }

  private stopRecording(): void {
    this.isRecording = false;
    this.recordingStartTime = null;
    // In a real implementation, this would stop recording and save the file
    console.log('Recording stopped');
  }
}

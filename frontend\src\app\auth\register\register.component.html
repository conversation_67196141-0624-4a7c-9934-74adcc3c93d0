<div class="container-fluid vh-100">
  <div class="row h-100">
    <!-- Left side - Branding -->
    <div class="col-md-5 d-none d-md-flex align-items-center justify-content-center bg-primary text-white">
      <div class="text-center">
        <div class="mb-4 d-flex justify-content-center">
          <div class="healthconnect-logo logo-xl">
            <div class="logo-icon">
              <i class="bi bi-heart-pulse"></i>
            </div>
          </div>
        </div>
        <h1 class="display-4 fw-bold mb-3">HealthConnect</h1>
        <p class="lead mb-4">Join our trusted medical platform for seamless healthcare management</p>
        <div class="mt-4">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="bi bi-check-circle me-2"></i>
            <span>Secure Patient-Doctor Communication</span>
          </div>
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="bi bi-check-circle me-2"></i>
            <span>Easy Appointment Scheduling</span>
          </div>
          <div class="d-flex align-items-center justify-content-center">
            <i class="bi bi-check-circle me-2"></i>
            <span>AI-Powered Health Assistance</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Registration Form -->
    <div class="col-md-7 d-flex align-items-center justify-content-center p-4">
      <div class="w-100" style="max-width: 500px;">
        <div class="register-form-container">
          <!-- Header -->
          <div class="text-center mb-4">
            <div class="mb-3 d-flex justify-content-center">
              <div class="healthconnect-logo logo-md">
                <div class="logo-icon">
                  <i class="bi bi-heart-pulse"></i>
                </div>
              </div>
            </div>
            <h2 class="fw-bold text-dark mb-2">Join HealthConnect</h2>
            <p class="text-muted">Create your account to get started</p>
          </div>

          <!-- Success Alert -->
          <div *ngIf="successMessage" class="alert alert-success" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            {{ successMessage }}
          </div>

          <!-- Error Alert -->
          <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>

          <!-- Loading State -->
          <div *ngIf="!registerForm" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Initializing form...</p>
          </div>

          <!-- Registration Form -->
          <form *ngIf="registerForm" [formGroup]="registerForm" (ngSubmit)="onSubmit()" novalidate>

            <!-- Role Selection -->
            <div class="mb-4">
              <label class="form-label fw-semibold">I am a:</label>
              <div class="row">
                <div class="col-6">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="role"
                      id="rolePatient"
                      value="PATIENT"
                      formControlName="role"
                    >
                    <label class="form-check-label" for="rolePatient">
                      <i class="bi bi-person me-2"></i>Patient
                    </label>
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="role"
                      id="roleDoctor"
                      value="DOCTOR"
                      formControlName="role"
                    >
                    <label class="form-check-label" for="roleDoctor">
                      <i class="bi bi-person-badge me-2"></i>Doctor
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Basic Information -->
            <div class="row">
              <div class="col-12 mb-3">
                <label for="fullName" class="form-label">Full Name *</label>
                <input
                  type="text"
                  class="form-control"
                  id="fullName"
                  formControlName="fullName"
                  placeholder="Enter your full name"
                  [class.is-invalid]="isFieldInvalid('fullName')"
                >
                <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
                  {{ getFieldError('fullName') }}
                </div>
              </div>

              <div class="col-12 mb-3">
                <label for="email" class="form-label">Email Address *</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  formControlName="email"
                  placeholder="Enter your email"
                  [class.is-invalid]="isFieldInvalid('email')"
                >
                <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
                  {{ getFieldError('email') }}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="password" class="form-label">Password *</label>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  formControlName="password"
                  placeholder="Create a password"
                  [class.is-invalid]="isFieldInvalid('password')"
                >
                <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
                  {{ getFieldError('password') }}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                <input
                  type="password"
                  class="form-control"
                  id="confirmPassword"
                  formControlName="confirmPassword"
                  placeholder="Confirm your password"
                  [class.is-invalid]="isFieldInvalid('confirmPassword')"
                >
                <div *ngIf="isFieldInvalid('confirmPassword')" class="invalid-feedback">
                  {{ getFieldError('confirmPassword') }}
                </div>
              </div>
            </div>

            <!-- Doctor-specific fields -->
            <div *ngIf="selectedRole === 'DOCTOR'" class="border-top pt-3 mb-3">
              <h6 class="fw-semibold text-primary mb-3">
                <i class="bi bi-person-badge me-2"></i>Doctor Information
              </h6>
              
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="specialization" class="form-label">Specialization *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="specialization"
                    formControlName="specialization"
                    placeholder="e.g., Cardiology"
                    [class.is-invalid]="isFieldInvalid('specialization')"
                  >
                  <div *ngIf="isFieldInvalid('specialization')" class="invalid-feedback">
                    {{ getFieldError('specialization') }}
                  </div>
                </div>

                <div class="col-md-6 mb-3">
                  <label for="licenseNumber" class="form-label">License Number *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="licenseNumber"
                    formControlName="licenseNumber"
                    placeholder="Medical license number"
                    [class.is-invalid]="isFieldInvalid('licenseNumber')"
                  >
                  <div *ngIf="isFieldInvalid('licenseNumber')" class="invalid-feedback">
                    {{ getFieldError('licenseNumber') }}
                  </div>
                </div>

                <div class="col-md-6 mb-3">
                  <label for="affiliation" class="form-label">Hospital/Clinic</label>
                  <input
                    type="text"
                    class="form-control"
                    id="affiliation"
                    formControlName="affiliation"
                    placeholder="Your workplace"
                  >
                </div>

                <div class="col-md-6 mb-3">
                  <label for="yearsOfExperience" class="form-label">Years of Experience</label>
                  <input
                    type="number"
                    class="form-control"
                    id="yearsOfExperience"
                    formControlName="yearsOfExperience"
                    placeholder="0"
                    min="0"
                    max="50"
                  >
                </div>
              </div>
            </div>

            <!-- Optional Contact Information -->
            <div class="border-top pt-3 mb-4">
              <h6 class="fw-semibold mb-3">
                <i class="bi bi-telephone me-2"></i>Contact Information (Optional)
              </h6>
              
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="phoneNumber" class="form-label">Phone Number</label>
                  <input
                    type="tel"
                    class="form-control"
                    id="phoneNumber"
                    formControlName="phoneNumber"
                    placeholder="Your phone number"
                  >
                </div>

                <div class="col-md-6 mb-3">
                  <label for="address" class="form-label">Address</label>
                  <input
                    type="text"
                    class="form-control"
                    id="address"
                    formControlName="address"
                    placeholder="Your address"
                  >
                </div>
              </div>
            </div>

            <!-- Premium Submit Button -->
            <button
              type="submit"
              class="btn btn-premium w-100 mb-3"
              [disabled]="isLoading"
            >
              <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <span *ngIf="isLoading">Creating Account...</span>
              <span *ngIf="!isLoading">
                <i class="bi bi-person-plus me-2"></i>Create Account
              </span>
            </button>
          </form>

          <!-- Login Link -->
          <div class="text-center">
            <p class="text-muted mb-0">
              Already have an account?
              <a routerLink="/auth/login" class="text-primary text-decoration-none fw-semibold ms-1"
                 style="color: #0d9488 !important;">
                Sign In
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
          <i class="fas fa-video me-2 text-primary"></i>
          Video Consultations
        </h2>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary" (click)="loadConsultations()">
            <i class="fas fa-sync-alt me-2"></i>
            Refresh
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Loading consultations...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && !error && consultations.length === 0" class="text-center py-5">
        <i class="fas fa-video fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No Video Consultations</h4>
        <p class="text-muted">You don't have any video consultations scheduled.</p>
        <a class="btn btn-primary" routerLink="/appointments">
          <i class="fas fa-plus me-2"></i>
          Schedule Appointment
        </a>
      </div>

      <!-- Consultations List -->
      <div *ngIf="!isLoading && !error && consultations.length > 0" class="row">
        <div *ngFor="let consultation of consultations" class="col-md-6 col-lg-4 mb-4">
          <div class="card h-100 shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
              <span class="badge" [class]="'bg-' + getStatusColor(consultation.status)">
                {{ getStatusLabel(consultation.status) }}
              </span>
              <small class="text-muted">
                {{ formatDateTime(consultation.scheduledStartTime) }}
              </small>
            </div>
            
            <div class="card-body">
              <h6 class="card-title">
                <i class="fas fa-user-md me-2"></i>
                {{ getOtherPartyName(consultation) }}
              </h6>
              
              <p class="card-text text-muted small">
                <i class="fas fa-calendar me-2"></i>
                {{ formatDate(consultation.scheduledStartTime) }}
              </p>
              
              <p class="card-text text-muted small">
                <i class="fas fa-clock me-2"></i>
                {{ formatTime(consultation.scheduledStartTime) }}
              </p>
              
              <p *ngIf="consultation.type" class="card-text text-muted small">
                <i class="fas fa-tag me-2"></i>
                {{ getTypeLabel(consultation.type) }}
              </p>
            </div>
            
            <div class="card-footer bg-transparent">
              <div class="d-flex gap-2 flex-wrap">
                <!-- Start Consultation (Doctor only) -->
                <button 
                  *ngIf="canStart(consultation)"
                  class="btn btn-success btn-sm"
                  (click)="onStartConsultation(consultation)">
                  <i class="fas fa-play me-1"></i>
                  Start
                </button>
                
                <!-- Join Consultation -->
                <button 
                  *ngIf="canJoin(consultation)"
                  class="btn btn-primary btn-sm"
                  (click)="onJoinConsultation(consultation)">
                  <i class="fas fa-video me-1"></i>
                  Join
                </button>
                
                <!-- View Details -->
                <button 
                  class="btn btn-outline-secondary btn-sm"
                  (click)="onViewConsultation(consultation)">
                  <i class="fas fa-eye me-1"></i>
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

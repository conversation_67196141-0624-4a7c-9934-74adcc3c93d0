// Profile component styles

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

.form-control[readonly] {
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

.text-primary {
  color: #0d6efd !important;
}

.btn-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
  border: none;
  font-weight: 500;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary {
  border-color: #0d6efd;
  color: #0d6efd;
  font-weight: 500;
}

.btn-outline-secondary {
  font-weight: 500;
}

.alert {
  border: none;
  border-radius: 8px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
}

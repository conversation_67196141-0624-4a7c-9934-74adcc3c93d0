// Patient dashboard specific styles

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

.activity-item,
.tip-item {
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 1rem;
}

.activity-item:last-child,
.tip-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.bg-primary {
  background-color: #0d6efd !important;
}

.bg-info {
  background-color: #0dcaf0 !important;
}

.bg-success {
  background-color: #198754 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

.text-primary {
  color: #0d6efd !important;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .display-6 {
    font-size: 2rem;
  }
}

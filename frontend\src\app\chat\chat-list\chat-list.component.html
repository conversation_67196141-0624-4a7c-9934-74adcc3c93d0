<div class="chat-list">
  <div class="chat-list-header">
    <h5 class="mb-0">
      <i class="bi bi-chat-dots me-2"></i>
      Messages
    </h5>
  </div>

  <div class="chat-list-body">
    <div *ngIf="loading" class="text-center p-3">
      <div class="spinner-border spinner-border-sm" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <div class="mt-2">Loading chats...</div>
    </div>

    <div *ngIf="!loading && chats.length === 0" class="text-center p-4 text-muted">
      <i class="bi bi-chat-square-text fs-1 mb-3 d-block"></i>
      <p>No conversations yet</p>
      <small>Start a conversation with a {{ currentUser?.role === 'PATIENT' ? 'doctor' : 'patient' }}</small>
    </div>

    <div *ngIf="!loading && chats.length > 0" class="chat-items">
      <div 
        *ngFor="let chat of chats" 
        class="chat-item"
        [class.active]="selectedChatId === chat.id"
        (click)="selectChat(chat)">
        
        <div class="chat-avatar">
          <img
            [src]="getOtherParticipant(chat).avatar || 'assets/images/default-avatar.svg'"
            [alt]="getOtherParticipant(chat).fullName"
            class="avatar-img">
          <span class="online-indicator" *ngIf="false"></span>
        </div>

        <div class="chat-content">
          <div class="chat-header">
            <h6 class="chat-name mb-0">
              {{ getOtherParticipant(chat).fullName }}
            </h6>
            <small class="chat-time text-muted">
              {{ chat.lastMessage ? formatLastMessageTime(chat.lastMessage.createdAt) : formatLastMessageTime(chat.createdAt) }}
            </small>
          </div>

          <div class="chat-preview">
            <p class="mb-0 text-muted" *ngIf="chat.lastMessage">
              <span *ngIf="chat.lastMessage.sender.id === currentUser?.id" class="me-1">
                <i class="bi bi-check2-all" 
                   [class.text-primary]="chat.lastMessage.status === 'READ'"
                   [class.text-muted]="chat.lastMessage.status !== 'READ'"></i>
              </span>
              {{ chat.lastMessage.content | slice:0:50 }}{{ chat.lastMessage.content.length > 50 ? '...' : '' }}
            </p>
            <p class="mb-0 text-muted" *ngIf="!chat.lastMessage">
              <em>No messages yet</em>
            </p>
          </div>
        </div>

        <div class="chat-meta">
          <span 
            *ngIf="chat.unreadCount > 0" 
            class="badge bg-primary rounded-pill">
            {{ chat.unreadCount }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

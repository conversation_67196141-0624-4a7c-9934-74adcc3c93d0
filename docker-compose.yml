version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: healthconnect-postgres
    environment:
      POSTGRES_DB: healthconnect
      POSTGRES_USER: healthconnect_user
      POSTGRES_PASSWORD: healthconnect_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - healthconnect-network
    restart: unless-stopped

  # Backend Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: healthconnect-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: *********************************************
      SPRING_DATASOURCE_USERNAME: healthconnect_user
      SPRING_DATASOURCE_PASSWORD: healthconnect_password
      JWT_SECRET: ${JWT_SECRET:-mySecretKeyForHealthConnectPlatformThatIsLongEnoughForSecurity}
      GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY:-AIzaSyCFWc5GegGFmlMpD7-gNyfXO_eiU24PklQ}
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:4200,https://yourdomain.com}
      FILE_UPLOAD_DIR: /app/uploads
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploads
    depends_on:
      - postgres
    networks:
      - healthconnect-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: healthconnect-frontend
    environment:
      NODE_ENV: production
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - healthconnect-network
    restart: unless-stopped

  # Redis for Session Management (Optional)
  redis:
    image: redis:7-alpine
    container_name: healthconnect-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - healthconnect-network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  backend_uploads:
    driver: local
  redis_data:
    driver: local

networks:
  healthconnect-network:
    driver: bridge

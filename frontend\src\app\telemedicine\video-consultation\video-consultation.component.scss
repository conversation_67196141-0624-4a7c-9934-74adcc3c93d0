.info-item {
  margin-bottom: 1rem;
  
  label {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
  }
  
  p {
    font-size: 1rem;
    color: #333;
  }
}

.participant-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.card {
  border: none;
  border-radius: 12px;
  
  .card-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  
  &.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
  }
}

.badge {
  font-size: 0.8rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

.font-monospace {
  font-family: 'Courier New', monospace;
  background-color: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Status colors
.bg-scheduled { background-color: #6c757d !important; }
.bg-waiting_for_doctor { background-color: #ffc107 !important; }
.bg-waiting_for_patient { background-color: #fd7e14 !important; }
.bg-in_progress { background-color: #198754 !important; }
.bg-completed { background-color: #0d6efd !important; }
.bg-cancelled { background-color: #dc3545 !important; }
.bg-no_show { background-color: #6f42c1 !important; }
.bg-technical_issues { background-color: #d63384 !important; }

// Responsive design
@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }
  
  .btn-lg {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .d-flex.gap-3 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
}

// Animation for loading
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

// Video calling styles
.video-container {
  position: relative;
  background: #000;
  min-height: 500px;
  overflow: hidden;
}

.main-video-area {
  position: relative;
  width: 100%;
  height: 500px;
  background: #1a1a1a;
}

.main-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #333;
}

.video-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.participant-info {
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.pip-video-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  height: 180px;
  border: 3px solid #28a745;
  border-radius: 12px;
  overflow: hidden;
  z-index: 10;
  background: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.pip-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #444;
}

.pip-label {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.video-controls {
  border-top: 1px solid #444;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 2s infinite;
}

.call-duration {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

// Video controls
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;

  i {
    font-size: 0.9rem;
  }
}

// Responsive video layout
@media (max-width: 768px) {
  .video-container {
    min-height: 300px;
  }

  .local-video-wrapper {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .remote-video-wrapper {
    height: 300px;
  }
}

package com.healthconnect.controller;

import com.healthconnect.dto.TimeSlotResponse;
import com.healthconnect.entity.User;
import com.healthconnect.entity.UserRole;
import com.healthconnect.repository.UserRepository;
import com.healthconnect.service.AppointmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/doctors")
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:4200"})
public class DoctorController {
    
    private final UserRepository userRepository;
    private final AppointmentService appointmentService;
    
    // Get all doctors with optional specialization filter
    @GetMapping
    public ResponseEntity<List<User>> getDoctors(
            @RequestParam(required = false) String specialization) {
        
        List<User> doctors;
        
        if (specialization != null && !specialization.trim().isEmpty()) {
            doctors = userRepository.findByRoleAndSpecializationContainingIgnoreCaseAndIsActiveTrue(
                    UserRole.DOCTOR, specialization);
        } else {
            doctors = userRepository.findByRoleAndIsActiveTrue(UserRole.DOCTOR);
        }
        
        return ResponseEntity.ok(doctors);
    }
    
    // Get doctor by ID
    @GetMapping("/{id}")
    public ResponseEntity<User> getDoctor(@PathVariable Long id) {
        return userRepository.findByIdAndRoleAndIsActiveTrue(id, UserRole.DOCTOR)
                .map(doctor -> ResponseEntity.ok(doctor))
                .orElse(ResponseEntity.notFound().build());
    }
    
    // Get available time slots for a doctor
    @GetMapping("/{doctorId}/time-slots")
    public ResponseEntity<List<TimeSlotResponse>> getAvailableTimeSlots(
            @PathVariable Long doctorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        try {
            List<TimeSlotResponse> timeSlots = appointmentService.getAvailableTimeSlots(doctorId, date);
            return ResponseEntity.ok(timeSlots);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    // Get doctor's specializations (for filtering)
    @GetMapping("/specializations")
    public ResponseEntity<List<String>> getSpecializations() {
        List<String> specializations = userRepository.findDistinctSpecializations();
        return ResponseEntity.ok(specializations);
    }
}

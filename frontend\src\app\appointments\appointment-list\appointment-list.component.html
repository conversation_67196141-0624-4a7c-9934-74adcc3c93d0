<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h4 class="card-title mb-0">
            <i class="fas fa-calendar-alt me-2"></i>My Appointments
          </h4>
          <button 
            *ngIf="isPatient()" 
            class="btn btn-primary"
            routerLink="/appointments/book">
            <i class="fas fa-plus me-2"></i>
            Book Appointment
          </button>
        </div>
        <div class="card-body">
          <!-- Filters -->
          <div class="row mb-4">
            <div class="col-md-4">
              <label for="statusFilter" class="form-label">Filter by Status</label>
              <select 
                id="statusFilter" 
                class="form-select"
                [(ngModel)]="statusFilter"
                (change)="onStatusFilterChange()">
                <option *ngFor="let option of statusOptions" [value]="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="typeFilter" class="form-label">Filter by Type</label>
              <select 
                id="typeFilter" 
                class="form-select"
                [(ngModel)]="typeFilter"
                (change)="onTypeFilterChange()">
                <option *ngFor="let option of typeOptions" [value]="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
              <button 
                class="btn btn-outline-secondary"
                (click)="loadAppointments()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
              </button>
            </div>
          </div>

          <!-- Error Message -->
          <div *ngIf="error" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading appointments...</p>
          </div>

          <!-- Appointments List -->
          <div *ngIf="!loading && filteredAppointments.length > 0" class="appointments-list">
            <div class="row">
              <div class="col-lg-6 mb-4" *ngFor="let appointment of filteredAppointments">
                <div class="card appointment-card h-100">
                  <div class="card-body">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                      <div>
                        <h6 class="card-title mb-1">
                          {{ getOtherParty(appointment) }}
                        </h6>
                        <small class="text-muted">{{ getOtherPartyRole(appointment) }}</small>
                      </div>
                      <span class="badge" [ngClass]="getStatusBadgeClass(appointment.status)">
                        {{ getStatusDisplayName(appointment.status) }}
                      </span>
                    </div>

                    <!-- Appointment Details -->
                    <div class="appointment-details">
                      <p class="mb-2">
                        <i class="fas fa-calendar me-2 text-muted"></i>
                        {{ formatDate(appointment.date) }}
                      </p>
                      <p class="mb-2">
                        <i class="fas fa-clock me-2 text-muted"></i>
                        {{ formatTime(appointment.startTime) }} - {{ formatTime(appointment.endTime) }}
                      </p>
                      <p class="mb-2">
                        <i class="fas fa-video me-2 text-muted" *ngIf="appointment.type === 'VIDEO_CALL'"></i>
                        <i class="fas fa-user-friends me-2 text-muted" *ngIf="appointment.type === 'IN_PERSON'"></i>
                        {{ getTypeDisplayName(appointment.type) }}
                      </p>
                      <p class="mb-3" *ngIf="appointment.reasonForVisit">
                        <i class="fas fa-notes-medical me-2 text-muted"></i>
                        {{ appointment.reasonForVisit }}
                      </p>
                    </div>

                    <!-- Meeting Link -->
                    <div *ngIf="appointment.meetingLink && appointment.type === 'VIDEO_CALL'" class="mb-3">
                      <a 
                        [href]="appointment.meetingLink" 
                        target="_blank" 
                        class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-video me-2"></i>
                        Join Video Call
                      </a>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="btn-group" role="group">
                        <button
                          class="btn btn-sm btn-outline-primary"
                          (click)="viewAppointment(appointment)">
                          <i class="fas fa-eye me-1"></i>
                          View
                        </button>

                        <!-- Chat Access Buttons -->
                        <app-chat-access
                          *ngIf="isBeforeAppointment(appointment)"
                          [config]="{
                            appointmentId: appointment.id,
                            doctorId: appointment.doctor.id,
                            patientId: appointment.patient.id,
                            chatType: 'PRE_APPOINTMENT',
                            buttonText: 'Chat',
                            buttonClass: 'btn-info',
                            size: 'sm',
                            showIcon: false
                          }">
                        </app-chat-access>

                        <app-chat-access
                          *ngIf="isAfterAppointment(appointment)"
                          [config]="{
                            appointmentId: appointment.id,
                            doctorId: appointment.doctor.id,
                            patientId: appointment.patient.id,
                            chatType: 'POST_APPOINTMENT',
                            buttonText: 'Follow-up',
                            buttonClass: 'btn-success',
                            size: 'sm',
                            showIcon: false
                          }">
                        </app-chat-access>
                      </div>

                      <div>
                        <button
                          *ngIf="canCancelAppointment(appointment)"
                          class="btn btn-sm btn-outline-danger"
                          (click)="cancelAppointment(appointment)">
                          <i class="fas fa-times me-1"></i>
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Appointments -->
          <div *ngIf="!loading && filteredAppointments.length === 0" class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No appointments found</h5>
            <p class="text-muted mb-4">
              <span *ngIf="isPatient()">You haven't booked any appointments yet.</span>
              <span *ngIf="isDoctor()">You don't have any appointments scheduled.</span>
            </p>
            <button 
              *ngIf="isPatient()" 
              class="btn btn-primary"
              routerLink="/appointments/book">
              <i class="fas fa-plus me-2"></i>
              Book Your First Appointment
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthConnect Frontend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 HealthConnect Frontend Test</h1>
        
        <div class="test-section info">
            <h3>📋 Manual Frontend Test</h3>
            <p>This page will test the HealthConnect frontend APIs directly from the browser.</p>
            <p><strong>Test Credentials:</strong></p>
            <ul>
                <li>Patient: <EMAIL> / password123</li>
                <li>Doctor: <EMAIL> / password123</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <button onclick="testLogin()">Test Patient Login</button>
            <button onclick="testDoctorLogin()">Test Doctor Login</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>👨‍⚕️ Doctors API Test</h3>
            <button onclick="testGetDoctors()">Get Available Doctors</button>
            <div id="doctors-result"></div>
        </div>

        <div class="test-section">
            <h3>📅 Time Slots API Test</h3>
            <button onclick="testGetTimeSlots()">Get Time Slots</button>
            <div id="slots-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Appointment Booking Test</h3>
            <button onclick="testBookAppointment()">Book Test Appointment</button>
            <div id="booking-result"></div>
        </div>

        <div class="test-section">
            <h3>💬 Chat API Test</h3>
            <button onclick="testChatAPI()">Test Chat Creation</button>
            <div id="chat-result"></div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let patientToken = null;
        let doctorToken = null;
        let doctorId = null;

        function log(message, type = 'info') {
            const resultDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultDiv.appendChild(div);
            console.log(message);
        }

        async function testLogin() {
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    document.getElementById('auth-result').innerHTML = 
                        `<div class="success">✅ Patient login successful! Token: ${patientToken.substring(0, 20)}...</div>`;
                    log('✅ Patient login successful', 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('auth-result').innerHTML = 
                        `<div class="error">❌ Patient login failed: ${response.status} - ${error}</div>`;
                    log(`❌ Patient login failed: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('auth-result').innerHTML = 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testDoctorLogin() {
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    doctorId = data.id;
                    document.getElementById('auth-result').innerHTML += 
                        `<div class="success">✅ Doctor login successful! ID: ${doctorId}</div>`;
                    log('✅ Doctor login successful', 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('auth-result').innerHTML += 
                        `<div class="error">❌ Doctor login failed: ${response.status} - ${error}</div>`;
                    log(`❌ Doctor login failed: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('auth-result').innerHTML += 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testGetDoctors() {
            if (!patientToken) {
                await testLogin();
            }

            try {
                const response = await fetch('http://localhost:8080/api/doctors', {
                    headers: {
                        'Authorization': `Bearer ${patientToken}`,
                    }
                });

                if (response.ok) {
                    const doctors = await response.json();
                    document.getElementById('doctors-result').innerHTML = 
                        `<div class="success">✅ Found ${doctors.length} doctors: ${doctors.map(d => d.fullName).join(', ')}</div>`;
                    log(`✅ Found ${doctors.length} doctors`, 'success');
                    if (doctors.length > 0) {
                        doctorId = doctors[0].id;
                    }
                } else {
                    const error = await response.text();
                    document.getElementById('doctors-result').innerHTML = 
                        `<div class="error">❌ Failed to get doctors: ${response.status} - ${error}</div>`;
                    log(`❌ Failed to get doctors: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('doctors-result').innerHTML = 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testGetTimeSlots() {
            if (!patientToken) {
                await testLogin();
            }
            if (!doctorId) {
                await testGetDoctors();
            }

            try {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const date = tomorrow.toISOString().split('T')[0];

                const response = await fetch(`http://localhost:8080/api/doctors/${doctorId}/time-slots?date=${date}`, {
                    headers: {
                        'Authorization': `Bearer ${patientToken}`,
                    }
                });

                if (response.ok) {
                    const slots = await response.json();
                    const availableSlots = slots.filter(slot => slot.available);
                    document.getElementById('slots-result').innerHTML = 
                        `<div class="success">✅ Found ${availableSlots.length} available time slots for ${date}</div>`;
                    log(`✅ Found ${availableSlots.length} available time slots`, 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('slots-result').innerHTML = 
                        `<div class="error">❌ Failed to get time slots: ${response.status} - ${error}</div>`;
                    log(`❌ Failed to get time slots: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('slots-result').innerHTML = 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testBookAppointment() {
            if (!patientToken) {
                await testLogin();
            }
            if (!doctorId) {
                await testGetDoctors();
            }

            try {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const date = tomorrow.toISOString().split('T')[0];

                const appointmentData = {
                    doctorId: doctorId,
                    date: date,
                    startTime: '14:00:00',
                    endTime: '14:30:00',
                    type: 'IN_PERSON',
                    reasonForVisit: 'Frontend test appointment',
                    notes: 'This is a test appointment from the frontend test page'
                };

                log(`📝 Booking appointment: ${JSON.stringify(appointmentData)}`, 'info');

                const response = await fetch('http://localhost:8080/api/appointments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${patientToken}`,
                    },
                    body: JSON.stringify(appointmentData)
                });

                if (response.ok) {
                    const appointment = await response.json();
                    document.getElementById('booking-result').innerHTML = 
                        `<div class="success">✅ Appointment booked successfully! ID: ${appointment.id}, Status: ${appointment.status}</div>`;
                    log(`✅ Appointment booked successfully! ID: ${appointment.id}`, 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('booking-result').innerHTML = 
                        `<div class="error">❌ Failed to book appointment: ${response.status} - ${error}</div>`;
                    log(`❌ Failed to book appointment: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                document.getElementById('booking-result').innerHTML = 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testChatAPI() {
            if (!patientToken) {
                await testLogin();
            }
            if (!doctorId) {
                await testGetDoctors();
            }

            try {
                const response = await fetch('http://localhost:8080/api/chats', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${patientToken}`,
                    },
                    body: JSON.stringify({
                        participantId: doctorId
                    })
                });

                if (response.ok) {
                    const chat = await response.json();
                    document.getElementById('chat-result').innerHTML = 
                        `<div class="success">✅ Chat created successfully! ID: ${chat.id}</div>`;
                    log(`✅ Chat created successfully! ID: ${chat.id}`, 'success');
                } else {
                    const error = await response.text();
                    document.getElementById('chat-result').innerHTML = 
                        `<div class="error">❌ Failed to create chat: ${response.status} - ${error}</div>`;
                    log(`❌ Failed to create chat: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('chat-result').innerHTML = 
                    `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            log('🚀 Frontend test page loaded', 'info');
            log('Click the buttons above to test different APIs', 'info');
        };
    </script>
</body>
</html>

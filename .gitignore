# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Angular
frontend/dist/
frontend/.angular/
frontend/node_modules/

# Backend
backend/target/
backend/.mvn/
backend/mvnw
backend/mvnw.cmd

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
dist/

# Temporary files
tmp/
temp/

# Database
*.db
*.sqlite

# Uploads (if you add file storage later)
uploads/

# Cache files
.cache/
*.cache

# Package files
*.tgz
*.tar.gz

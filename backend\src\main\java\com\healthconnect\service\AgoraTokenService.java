package com.healthconnect.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Service
@Slf4j
public class AgoraTokenService {

    @Value("${agora.app.id}")
    private String appId;

    @Value("${agora.app.certificate}")
    private String appCertificate;

    /**
     * Generate a simple token for Agora RTC
     * For production, use the official Agora token generation library
     */
    public String generateRtcToken(String channelName, int uid, int expireTimeInSeconds) {
        try {
            // For demo purposes, we'll create a simple token
            // In production, use io.agora.rtc.token.RtcTokenBuilder
            
            long currentTimestamp = System.currentTimeMillis() / 1000;
            long privilegeExpiredTs = currentTimestamp + expireTimeInSeconds;
            
            // Create a simple hash-based token for demo
            String tokenData = appId + channelName + uid + privilegeExpiredTs + appCertificate;
            String token = generateSimpleToken(tokenData);
            
            log.info("Generated token for channel: {}, uid: {}, expires: {}", channelName, uid, privilegeExpiredTs);
            return token;
            
        } catch (Exception e) {
            log.error("Error generating Agora token: {}", e.getMessage());
            // Return null for testing mode
            return null;
        }
    }

    /**
     * Generate a simple hash-based token for demo purposes
     */
    private String generateSimpleToken(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            log.error("Error creating token hash: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Validate if the app configuration is valid
     */
    public boolean isConfigurationValid() {
        return appId != null && !appId.isEmpty() && 
               appCertificate != null && !appCertificate.isEmpty();
    }

    /**
     * Get app configuration for client
     */
    public String getAppId() {
        return appId;
    }
}

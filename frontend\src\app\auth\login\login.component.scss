// Outstanding Medical Login Design - Premium Aesthetic
// Direct CSS variables for reliability

// HealthConnect Official Logo
.healthconnect-logo {
  display: flex;
  align-items: center;
  justify-content: center;

  .logo-icon {
    background: linear-gradient(135deg, #0d9488, #0f766e);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(20, 184, 166, 0.3);
    transition: all 0.3s ease;
    width: 60px;
    height: 60px;

    i {
      color: white;
      font-weight: 600;
      font-size: 1.5rem;
    }
  }

  &:hover .logo-icon {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(20, 184, 166, 0.4);
  }

  // XL size for branding panel
  &.logo-xl .logo-icon {
    width: 120px;
    height: 120px;
    border-radius: 32px;

    i {
      font-size: 3rem;
    }
  }
}

.vh-100 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0fdfa 0%, #ffffff 50%, #f0fdfa 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
      radial-gradient(circle at 20% 80%, rgba(20, 184, 166, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(94, 234, 212, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(20, 184, 166, 0.02) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
  }
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.bg-primary {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 50%, #134e4a 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: drift 30s linear infinite;
    pointer-events: none;
  }
}

@keyframes drift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-50px, -50px); }
}

.input-group-text {
  background-color: #ffffff;
  border-right: none;
  border-color: #a7f3d0;
  color: #0d9488;
}

.form-control {
  border: 2px solid #a7f3d0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 1rem;
  color: #374151;
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &::placeholder {
    color: #9ca3af;
    opacity: 0.8;
  }
}

.form-control:focus {
  background: #ffffff;
  border-color: #14b8a6;
  box-shadow:
    0 0 0 4px rgba(20, 184, 166, 0.1),
    0 4px 12px rgba(20, 184, 166, 0.15);
  transform: translateY(-2px);
}

.form-label {
  color: #374151;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: #0d9488 !important;
  border: none !important;
  font-weight: 500;
  color: #ffffff !important;
  transition: all 0.3s ease;
  border-radius: 12px !important;
}

.btn-primary:hover {
  background: #0f766e !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(20, 184, 166, 0.3);
}

.btn-primary:focus {
  background: #0f766e !important;
  box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.2) !important;
}

.btn-primary:disabled {
  background: var(--hc-gray-400);
  transform: none;
  box-shadow: none;
}

.alert {
  border: none;
  border-radius: var(--hc-radius-lg);
}

.form-check-input:checked {
  background-color: var(--hc-primary-600);
  border-color: var(--hc-primary-600);
}

// Additional theme overrides
.text-primary {
  color: var(--hc-primary-600) !important;
}

.text-dark {
  color: var(--hc-gray-800) !important;
}

.text-muted {
  color: var(--hc-gray-600) !important;
}

.bg-light {
  background-color: var(--hc-primary-50) !important;
  border: 1px solid var(--hc-primary-100);
}

.spinner-border.text-primary {
  color: var(--hc-primary-600) !important;
}

.form-label {
  color: var(--hc-gray-700);
  font-weight: 500;
}

// Premium Login Form Container
.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 60px rgba(20, 184, 166, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(20, 184, 166, 0.1);
  padding: 3rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--hc-primary-500), var(--hc-primary-400), var(--hc-primary-500));
    border-radius: 24px 24px 0 0;
  }
}

// Beautiful Form Styling
.form-floating {
  position: relative;
  margin-bottom: 1.5rem;

  .form-control {
    background: rgba(248, 250, 252, 0.8);
    border: 2px solid var(--hc-primary-100);
    border-radius: 16px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:focus {
      background: var(--hc-white);
      border-color: var(--hc-primary-400);
      box-shadow:
        0 0 0 4px rgba(20, 184, 166, 0.1),
        0 4px 12px rgba(20, 184, 166, 0.15);
      transform: translateY(-2px);
    }

    &::placeholder {
      color: var(--hc-gray-400);
      opacity: 0.8;
    }
  }

  label {
    color: var(--hc-gray-600);
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0 0.5rem;
    background: linear-gradient(to bottom, transparent 50%, var(--hc-white) 50%);
  }
}

// Premium Button Design
.btn-premium {
  background: linear-gradient(135deg, var(--hc-primary-500) 0%, var(--hc-primary-600) 100%);
  border: none;
  border-radius: 16px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  color: var(--hc-white);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(20, 184, 166, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, var(--hc-primary-600) 0%, var(--hc-primary-700) 100%);
    transform: translateY(-2px);
    box-shadow:
      0 8px 24px rgba(20, 184, 166, 0.4),
      0 4px 16px rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// Demo credentials styling
.demo-card {
  background: linear-gradient(135deg, var(--hc-primary-50) 0%, rgba(240, 253, 250, 0.8) 100%);
  border: 1px solid var(--hc-primary-200);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);

  h6 {
    color: var(--hc-primary-700);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '🔑';
      font-size: 1.2rem;
    }
  }

  small {
    color: var(--hc-gray-600) !important;
    font-weight: 500;
    display: block;
    padding: 0.25rem 0;

    &:hover {
      color: var(--hc-primary-700) !important;
      cursor: pointer;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }
  
  .display-1 {
    font-size: 3rem;
  }
  
  .display-4 {
    font-size: 2rem;
  }
}

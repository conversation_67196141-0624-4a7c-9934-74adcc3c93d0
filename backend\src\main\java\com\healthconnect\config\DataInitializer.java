package com.healthconnect.config;

import com.healthconnect.entity.User;
import com.healthconnect.entity.UserRole;
import com.healthconnect.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        createTestUsers();
    }

    private void createTestUsers() {
        // Create test patient
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User patient = new User();
            patient.setEmail("<EMAIL>");
            patient.setPassword(passwordEncoder.encode("password123"));
            patient.setFullName("Test Patient");
            patient.setRole(UserRole.PATIENT);
            patient.setPhoneNumber("**********");
            patient.setDateOfBirth(java.time.LocalDate.of(1990, 1, 1));
            patient.setGender("Male");
            patient.setAddress("123 Test Street");
            userRepository.save(patient);
            log.info("Created test patient: {}", patient.getEmail());
        }

        // Create test doctor
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User doctor = new User();
            doctor.setEmail("<EMAIL>");
            doctor.setPassword(passwordEncoder.encode("password123"));
            doctor.setFullName("Dr. Test Doctor");
            doctor.setRole(UserRole.DOCTOR);
            doctor.setPhoneNumber("**********");
            doctor.setDateOfBirth(java.time.LocalDate.of(1980, 1, 1));
            doctor.setGender("Female");
            doctor.setAddress("456 Medical Center");
            doctor.setSpecialization("General Medicine");
            doctor.setLicenseNumber("DOC123456");
            doctor.setExperience(10);
            doctor.setConsultationFee(100.0);
            userRepository.save(doctor);
            log.info("Created test doctor: {}", doctor.getEmail());
        }

        // Create additional test doctor
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User doctor2 = new User();
            doctor2.setEmail("<EMAIL>");
            doctor2.setPassword(passwordEncoder.encode("password123"));
            doctor2.setFullName("Dr. Sarah Johnson");
            doctor2.setRole(UserRole.DOCTOR);
            doctor2.setPhoneNumber("**********");
            doctor2.setDateOfBirth(java.time.LocalDate.of(1985, 5, 15));
            doctor2.setGender("Female");
            doctor2.setAddress("789 Health Plaza");
            doctor2.setSpecialization("Cardiology");
            doctor2.setLicenseNumber("DOC789012");
            doctor2.setExperience(8);
            doctor2.setConsultationFee(150.0);
            userRepository.save(doctor2);
            log.info("Created test doctor 2: {}", doctor2.getEmail());
        }

        log.info("Data initialization completed");
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual Chat System Test - Patient & Doctor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
            gap: 2px;
        }
        
        .chat-panel {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            position: relative;
        }
        
        .patient-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .doctor-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .user-details h3 {
            margin-bottom: 5px;
            font-size: 18px;
        }
        
        .user-details p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
            position: relative;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }
        
        .message.sent {
            flex-direction: row-reverse;
        }
        
        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }
        
        .message.sent .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.received .message-bubble {
            background: white;
            color: #374151;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .patient-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .doctor-avatar {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 20px;
            font-style: italic;
            color: #6b7280;
            font-size: 14px;
        }
        
        .typing-dots {
            display: inline-flex;
            gap: 3px;
            margin-left: 5px;
        }
        
        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        .message-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
        }
        
        .message-input {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            transition: border-color 0.2s;
        }
        
        .input-field:focus {
            border-color: #667eea;
        }
        
        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        
        .send-button:hover {
            transform: scale(1.05);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .connection-status.connected {
            background: rgba(34, 197, 94, 0.9);
        }
        
        .connection-status.connecting {
            background: rgba(251, 191, 36, 0.9);
        }
        
        .quick-responses {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }
        
        .quick-response {
            padding: 6px 12px;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-response:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }
        
        .divider {
            width: 2px;
            background: linear-gradient(to bottom, transparent, #e5e7eb, transparent);
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .divider {
                height: 2px;
                width: 100%;
                background: linear-gradient(to right, transparent, #e5e7eb, transparent);
            }
        }
    </style>
</head>
<body>
    <div class="connection-status connecting" id="connectionStatus">
        <div class="status-dot"></div>
        Connecting to chat server...
    </div>

    <div class="container">
        <!-- Patient Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header patient-header">
                <div class="user-info">
                    <div class="avatar">👤</div>
                    <div class="user-details">
                        <h3>John Smith</h3>
                        <p>Patient • Age 34</p>
                    </div>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    Online
                </div>
            </div>
            
            <div class="messages-container" id="patientMessages">
                <div class="message received">
                    <div class="message-avatar doctor-avatar">👨‍⚕️</div>
                    <div>
                        <div class="message-bubble">
                            Hello John! I'm Dr. Sarah Johnson. How are you feeling today?
                        </div>
                        <div class="message-time">2:30 PM</div>
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="patientTyping">
                Dr. Johnson is typing
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="message-input-container">
                <div class="quick-responses">
                    <div class="quick-response" onclick="sendQuickResponse('patient', 'Hello Doctor')">Hello Doctor</div>
                    <div class="quick-response" onclick="sendQuickResponse('patient', 'I have a question')">I have a question</div>
                    <div class="quick-response" onclick="sendQuickResponse('patient', 'Thank you')">Thank you</div>
                </div>
                <div class="message-input">
                    <textarea 
                        class="input-field" 
                        id="patientInput" 
                        placeholder="Type your message to Dr. Johnson..."
                        onkeypress="handleEnter(event, 'patient')"
                        oninput="handleTyping('patient')"></textarea>
                    <button class="send-button" onclick="sendMessage('patient')">
                        ➤
                    </button>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- Doctor Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header doctor-header">
                <div class="user-info">
                    <div class="avatar">👨‍⚕️</div>
                    <div class="user-details">
                        <h3>Dr. Sarah Johnson</h3>
                        <p>Cardiologist • 15 years exp.</p>
                    </div>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    Online
                </div>
            </div>
            
            <div class="messages-container" id="doctorMessages">
                <div class="message sent">
                    <div class="message-avatar doctor-avatar">👨‍⚕️</div>
                    <div>
                        <div class="message-bubble">
                            Hello John! I'm Dr. Sarah Johnson. How are you feeling today?
                        </div>
                        <div class="message-time">2:30 PM</div>
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="doctorTyping">
                John is typing
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="message-input-container">
                <div class="quick-responses">
                    <div class="quick-response" onclick="sendQuickResponse('doctor', 'How can I help you?')">How can I help?</div>
                    <div class="quick-response" onclick="sendQuickResponse('doctor', 'Please describe your symptoms')">Describe symptoms</div>
                    <div class="quick-response" onclick="sendQuickResponse('doctor', 'I recommend...')">I recommend...</div>
                </div>
                <div class="message-input">
                    <textarea 
                        class="input-field" 
                        id="doctorInput" 
                        placeholder="Type your message to John..."
                        onkeypress="handleEnter(event, 'doctor')"
                        oninput="handleTyping('doctor')"></textarea>
                    <button class="send-button" onclick="sendMessage('doctor')">
                        ➤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messageId = 1;
        let typingTimeouts = {};
        let isConnected = false;

        // Initialize chat system
        function initializeChat() {
            const status = document.getElementById('connectionStatus');
            
            // Simulate WebSocket connection
            setTimeout(() => {
                status.className = 'connection-status connected';
                status.innerHTML = '<div class="status-dot"></div>Chat server connected - Real-time messaging active';
                isConnected = true;
                
                // Hide status after 3 seconds
                setTimeout(() => {
                    status.style.opacity = '0';
                    setTimeout(() => {
                        status.style.display = 'none';
                    }, 300);
                }, 3000);
            }, 2000);
        }

        function sendMessage(sender) {
            if (!isConnected) {
                alert('Chat system is still connecting. Please wait...');
                return;
            }

            const inputId = sender + 'Input';
            const input = document.getElementById(inputId);
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add message to sender's view
            addMessage(sender, message, 'sent', sender === 'patient' ? '👤' : '👨‍⚕️');
            
            // Add message to receiver's view
            const receiver = sender === 'patient' ? 'doctor' : 'patient';
            addMessage(receiver, message, 'received', sender === 'patient' ? '👤' : '👨‍⚕️');
            
            input.value = '';
            
            // Show typing indicator for receiver and simulate response
            if (sender === 'patient') {
                simulateResponse('doctor', 'patient');
            } else {
                simulateResponse('patient', 'doctor');
            }
        }

        function sendQuickResponse(sender, message) {
            const input = document.getElementById(sender + 'Input');
            input.value = message;
            sendMessage(sender);
        }

        function addMessage(viewType, message, messageType, avatar) {
            const container = document.getElementById(viewType + 'Messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${messageType}`;
            
            const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const avatarClass = avatar === '👤' ? 'patient-avatar' : 'doctor-avatar';
            
            messageDiv.innerHTML = `
                <div class="message-avatar ${avatarClass}">${avatar}</div>
                <div>
                    <div class="message-bubble">${message}</div>
                    <div class="message-time">${currentTime}</div>
                </div>
            `;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
            
            // Add subtle animation
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateY(20px)';
            setTimeout(() => {
                messageDiv.style.transition = 'all 0.3s ease';
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            }, 50);
        }

        function simulateResponse(responder, originalSender) {
            const typingIndicator = document.getElementById(responder + 'Typing');
            
            // Show typing indicator
            typingIndicator.style.display = 'block';
            
            // Generate response after 1-3 seconds
            setTimeout(() => {
                typingIndicator.style.display = 'none';
                
                const responses = {
                    doctor: [
                        "Thank you for sharing that information with me.",
                        "I understand your concern. Let me help you with that.",
                        "Based on what you've described, I'd like to ask a few more questions.",
                        "That's a very good question. Here's what I recommend...",
                        "I see. Can you tell me more about when this started?",
                        "Let's schedule a follow-up to monitor your progress.",
                        "Please don't hesitate to reach out if you have more questions."
                    ],
                    patient: [
                        "Thank you, Doctor. That's very helpful.",
                        "I appreciate your time and expertise.",
                        "Yes, I have been experiencing that symptom.",
                        "When would be the best time for a follow-up?",
                        "Should I be concerned about any side effects?",
                        "How long should I expect this treatment to take?",
                        "I feel much better after our conversation."
                    ]
                };
                
                const responseList = responses[responder];
                const randomResponse = responseList[Math.floor(Math.random() * responseList.length)];
                const avatar = responder === 'patient' ? '👤' : '👨‍⚕️';
                
                // Add response to both views
                addMessage(responder, randomResponse, 'sent', avatar);
                addMessage(originalSender, randomResponse, 'received', avatar);
                
            }, 1000 + Math.random() * 2000);
        }

        function handleEnter(event, sender) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage(sender);
            }
        }

        function handleTyping(sender) {
            const receiver = sender === 'patient' ? 'doctor' : 'patient';
            const typingIndicator = document.getElementById(receiver + 'Typing');
            const senderName = sender === 'patient' ? 'John' : 'Dr. Johnson';
            
            // Show typing indicator
            typingIndicator.innerHTML = `
                ${senderName} is typing
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            typingIndicator.style.display = 'block';
            
            // Clear previous timeout
            if (typingTimeouts[sender]) {
                clearTimeout(typingTimeouts[sender]);
            }
            
            // Hide typing indicator after 2 seconds of no typing
            typingTimeouts[sender] = setTimeout(() => {
                typingIndicator.style.display = 'none';
            }, 2000);
        }

        // Auto-resize textareas
        document.querySelectorAll('.input-field').forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        });

        // Initialize when page loads
        window.onload = initializeChat;
    </script>
</body>
</html>

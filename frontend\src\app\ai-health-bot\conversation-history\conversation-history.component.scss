// Modern Conversation History Container
.conversation-history-container {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  min-height: calc(100vh - 120px);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

// Modern Header Design
.history-header {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem;
  position: relative;
  z-index: 10;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 1rem;

      .history-icon {
        .icon-glow {
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.2rem;
          box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
          animation: pulse-glow 3s ease-in-out infinite;

          i {
            filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
          }
        }
      }

      .header-text {
        .history-title {
          color: #ffffff;
          font-size: 1.5rem;
          font-weight: 700;
          margin: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .history-subtitle {
          color: rgba(255, 255, 255, 0.7);
          font-size: 0.9rem;
          margin: 0;
          font-weight: 400;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 0.75rem;

      .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
        padding: 0.75rem 1.25rem;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        i {
          font-size: 0.85rem;
        }
      }
    }
  }
}

// Filters Section
.filters-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// History Content
.history-content {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  margin: 2rem;
  min-height: 400px;
  position: relative;
  z-index: 5;
}

.conversations-list {
  padding: 2rem;
}

.conversation-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);

    &::before {
      opacity: 1;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.conversation-type-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);

  i {
    font-size: 1rem;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
  }
}

.conversation-title {
  flex: 1;

  h6 {
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
    font-size: 1rem;
  }

  small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
  }
}

.conversation-meta {
  text-align: right;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.conversation-date {
  line-height: 1.3;

  small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
  }
}

.conversation-preview {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;

  p {
    margin-bottom: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.conversation-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  position: relative;
  z-index: 2;
}

.conversation-stats {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.75rem;

  .badge {
    background: rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);

    &.bg-success {
      background: rgba(34, 197, 94, 0.2) !important;
      border-color: rgba(34, 197, 94, 0.4);
      color: #4ade80 !important;
    }

    i {
      font-size: 0.7rem;
      margin-right: 0.25rem;
    }
  }
}

.conversation-actions {
  display: flex;
  align-items: center;

  i {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }
}

.empty-state {
  padding: 4rem 2rem;
  text-align: center;

  i {
    color: rgba(255, 255, 255, 0.3);
    margin-bottom: 1.5rem;
  }

  h5 {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
  }

  p {
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2rem;
  }

  .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    &.btn-outline-secondary {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.9);

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

// Form Controls
.filters-section {
  .input-group {
    .input-group-text {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(10px);
    }

    .form-control {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(102, 126, 234, 0.5);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .btn-outline-secondary {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.7);

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .form-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    &:focus {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(102, 126, 234, 0.5);
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    option {
      background: #1a1a2e;
      color: white;
    }
  }

  .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
  }
}

// Animations
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
    transform: scale(1.05);
  }
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: fa-spin 1s infinite linear;
}

// Loading and Error States
.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  backdrop-filter: blur(20px);
  border-radius: 12px;
  margin: 2rem;

  i {
    color: #ef4444;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .history-header {
    padding: 1.5rem;
    margin: 1.5rem;
  }

  .history-content {
    margin: 1.5rem;
  }

  .conversations-list {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .conversation-history-container {
    min-height: calc(100vh - 100px);
  }

  .history-header {
    padding: 1rem;
    margin: 1rem;

    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      .header-left {
        .history-icon .icon-glow {
          width: 40px;
          height: 40px;
          font-size: 1rem;
        }

        .header-text {
          .history-title {
            font-size: 1.3rem;
          }

          .history-subtitle {
            font-size: 0.85rem;
          }
        }
      }

      .header-actions {
        align-self: stretch;
        justify-content: space-between;

        .action-btn {
          flex: 1;
          justify-content: center;
          padding: 0.6rem 1rem;
          font-size: 0.85rem;

          span {
            display: none;
          }
        }
      }
    }
  }

  .history-content {
    margin: 1rem;
  }

  .conversations-list {
    padding: 1rem;
  }

  .conversation-card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .conversation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;

    .conversation-meta {
      text-align: left;
      width: 100%;
    }
  }

  .conversation-type-icon {
    width: 36px;
    height: 36px;
    margin-right: 0.75rem;

    i {
      font-size: 0.9rem;
    }
  }

  .conversation-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .empty-state {
    padding: 3rem 1rem;

    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
}

@media (max-width: 480px) {
  .history-header {
    .header-content {
      .header-actions {
        .action-btn {
          padding: 0.5rem 0.75rem;
          font-size: 0.8rem;

          i {
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  .conversation-card {
    padding: 0.75rem;

    .conversation-type-icon {
      width: 32px;
      height: 32px;
      margin-right: 0.5rem;

      i {
        font-size: 0.8rem;
      }
    }

    .conversation-title h6 {
      font-size: 0.9rem;
    }
  }
}

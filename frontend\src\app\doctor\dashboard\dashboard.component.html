<div class="container-fluid py-4">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3 text-muted">Loading your dashboard...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading && !error">
    <!-- Welcome Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1 class="h3 mb-1">{{ getGreeting() }}, Dr. {{ currentUser?.fullName }}!</h1>
            <p class="text-muted mb-0">
              <i class="bi bi-hospital me-2"></i>{{ currentUser?.specialization || 'General Practice' }}
              <span *ngIf="currentUser?.affiliation"> • {{ currentUser?.affiliation }}</span>
            </p>
          </div>
          <button class="btn btn-outline-primary" (click)="refreshData()">
            <i class="bi bi-arrow-clockwise me-2"></i>Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Dashboard Statistics -->
    <div class="row mb-4">
      <div class="col-md-3 col-sm-6 mb-3" *ngFor="let stat of dashboardStats">
        <div class="card h-100">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0 me-3">
                <i class="bi bi-{{ stat.icon }} fs-2 {{ stat.color }}"></i>
              </div>
              <div class="flex-grow-1">
                <h6 class="card-title text-muted mb-1">{{ stat.title }}</h6>
                <h3 class="mb-1">{{ stat.value }}</h3>
                <small [class]="getChangeClass(stat.changeType)">
                  <i [class]="getChangeIcon(stat.changeType)"></i>
                  {{ stat.change }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Today's Schedule & Recent Activities -->
    <div class="row">
      <!-- Today's Appointments -->
      <div class="col-md-8 mb-4">
        <div class="card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="bi bi-calendar-day me-2"></i>Today's Schedule
            </h6>
            <button class="btn btn-sm btn-outline-primary" (click)="navigateTo('/appointments')">
              View All
            </button>
          </div>
          <div class="card-body">
            <div *ngIf="realTodayAppointments.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-calendar-x display-6 mb-3"></i>
              <p>No appointments scheduled for today</p>
            </div>

            <div *ngFor="let appointment of realTodayAppointments" class="appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                  <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                       style="width: 45px; height: 45px;">
                    <i class="bi bi-{{ getAppointmentTypeIcon(appointment.type) }} {{ getAppointmentTypeClass(appointment.type) }}"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-1">{{ appointment.patient.fullName }}</h6>
                  <p class="mb-1 text-muted small">
                    <i class="bi bi-clock me-1"></i>{{ appointment.startTime }} - {{ appointment.endTime }}
                    <span class="ms-2">
                      <span [class]="getStatusBadgeClass(appointment.status)">
                        {{ appointment.status | titlecase }}
                      </span>
                    </span>
                  </p>
                  <small class="text-muted">
                    {{ appointment.type === 'VIDEO_CALL' ? 'Video Consultation' : 'In-Person Visit' }}
                    <span *ngIf="appointment.reasonForVisit"> • {{ appointment.reasonForVisit }}</span>
                  </small>
                </div>
              </div>
              <div class="flex-shrink-0">
                <button
                  *ngIf="appointment.type === 'VIDEO_CALL'"
                  class="btn btn-sm btn-success"
                  (click)="startVideoCall(appointment)"
                >
                  <i class="bi bi-camera-video me-1"></i>
                  Start Video Call
                </button>
                <button
                  *ngIf="appointment.type !== 'VIDEO_CALL'"
                  class="btn btn-sm btn-primary"
                  (click)="navigateTo('/appointments/' + appointment.id)"
                >
                  <i class="bi bi-person me-1"></i>
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-activity me-2"></i>Recent Activities
            </h6>
          </div>
          <div class="card-body">
            <div *ngFor="let activity of recentActivities; last as isLast" class="activity-item">
              <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                  <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                       style="width: 35px; height: 35px;">
                    <i class="bi bi-{{ activity.icon }} {{ activity.color }}"></i>
                  </div>
                </div>
                <div class="flex-grow-1">
                  <h6 class="mb-1 small">{{ activity.title }}</h6>
                  <p class="mb-1 text-muted small">{{ activity.description }}</p>
                  <small class="text-muted">{{ activity.time }}</small>
                </div>
              </div>
              <hr *ngIf="!isLast" class="my-3">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Messages Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="bi bi-chat-dots me-2"></i>Recent Messages
            </h6>
            <button class="btn btn-sm btn-outline-primary" (click)="openChatModal()">
              <i class="bi bi-chat-plus me-1"></i>View All Messages
            </button>
          </div>
          <div class="card-body">
            <div *ngIf="recentChats.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-chat-square-text display-6 mb-3"></i>
              <p>No messages yet</p>
            </div>

            <div *ngFor="let chat of recentChats" class="chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                  <img
                    [src]="chat.patient.avatar || '/assets/images/default-avatar.png'"
                    [alt]="chat.patient.fullName"
                    class="rounded-circle"
                    style="width: 45px; height: 45px; object-fit: cover;">
                </div>
                <div>
                  <h6 class="mb-1">{{ chat.patient.fullName }}</h6>
                  <p class="mb-1 text-muted small" *ngIf="chat.lastMessage">
                    {{ chat.lastMessage.content | slice:0:50 }}{{ chat.lastMessage.content.length > 50 ? '...' : '' }}
                  </p>
                  <small class="text-muted" *ngIf="chat.lastMessage">
                    {{ formatChatTime(chat.lastMessage.createdAt) }}
                  </small>
                </div>
              </div>
              <div class="flex-shrink-0">
                <span *ngIf="chat.unreadCount > 0" class="badge bg-primary rounded-pill me-2">
                  {{ chat.unreadCount }}
                </span>
                <button
                  class="btn btn-sm btn-outline-primary"
                  (click)="openChat(chat)"
                >
                  <i class="bi bi-chat me-1"></i>Reply
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-lightning me-2"></i>Quick Actions
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3 col-sm-6 mb-3">
                <button class="btn btn-outline-primary w-100 py-3" (click)="navigateTo('/patients')">
                  <i class="bi bi-people d-block fs-4 mb-2"></i>
                  <span>Manage Patients</span>
                </button>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <button class="btn btn-outline-success w-100 py-3" (click)="navigateTo('/appointments')">
                  <i class="bi bi-calendar-plus d-block fs-4 mb-2"></i>
                  <span>Schedule Appointment</span>
                </button>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <button class="btn btn-outline-info w-100 py-3" (click)="navigateTo('/chat')">
                  <i class="bi bi-chat-dots d-block fs-4 mb-2"></i>
                  <span>Messages</span>
                </button>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <button class="btn btn-outline-warning w-100 py-3" (click)="navigateTo('/reports')">
                  <i class="bi bi-graph-up d-block fs-4 mb-2"></i>
                  <span>View Reports</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chat Modal -->
<div class="modal fade" id="chatModal" tabindex="-1" aria-labelledby="chatModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="chatModalLabel">
          <i class="bi bi-chat-dots me-2"></i>Messages
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0" style="height: 600px;">
        <div class="row h-100 g-0">
          <div class="col-md-4 border-end">
            <app-chat-list (chatSelected)="onChatSelected($event)"></app-chat-list>
          </div>
          <div class="col-md-8">
            <app-chat-window #chatWindow></app-chat-window>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

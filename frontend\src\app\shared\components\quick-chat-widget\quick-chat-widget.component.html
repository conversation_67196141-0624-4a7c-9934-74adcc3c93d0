<div class="quick-chat-widget">
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">
        <i class="fas fa-comments text-primary me-2"></i>
        Quick Chat Access
      </h6>
      <button 
        type="button" 
        class="btn btn-sm btn-outline-primary"
        (click)="navigateToChat()">
        <i class="fas fa-external-link-alt me-1"></i>
        View All
      </button>
    </div>

    <div class="card-body">
      <!-- Recent Chats Section -->
      <div *ngIf="recentChats.length > 0" class="mb-4">
        <h6 class="text-muted mb-3">
          <i class="fas fa-clock me-2"></i>Recent Conversations
        </h6>
        
        <div class="chat-list">
          <div 
            *ngFor="let chat of recentChats"
            class="chat-item"
            (click)="navigateToChat(chat.id)">
            
            <div class="chat-avatar">
              <img 
                [src]="getOtherParticipant(chat)?.avatar || '/assets/images/default-avatar.png'"
                [alt]="getOtherParticipant(chat)?.fullName"
                class="rounded-circle">
            </div>
            
            <div class="chat-info">
              <div class="chat-name">{{ getOtherParticipant(chat)?.fullName }}</div>
              <div class="chat-preview">
                <span *ngIf="chat.lastMessage" class="text-muted">
                  {{ chat.lastMessage.content.length > 30 ? 
                      chat.lastMessage.content.substring(0, 30) + '...' : 
                      chat.lastMessage.content }}
                </span>
                <span *ngIf="!chat.lastMessage" class="text-muted">No messages yet</span>
              </div>
            </div>
            
            <div class="chat-meta">
              <small class="text-muted">
                {{ chat.updatedAt | date:'short' }}
              </small>
              <div *ngIf="chat.unreadCount > 0" class="badge bg-primary">
                {{ chat.unreadCount }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upcoming Appointments Section -->
      <div *ngIf="upcomingAppointments.length > 0">
        <h6 class="text-muted mb-3">
          <i class="fas fa-calendar-alt me-2"></i>Upcoming Appointments
        </h6>
        
        <div class="appointment-list">
          <div 
            *ngFor="let appointment of upcomingAppointments"
            class="appointment-item">
            
            <div class="appointment-info">
              <div class="appointment-doctor">
                <strong>{{ appointment.doctor.fullName }}</strong>
              </div>
              <div class="appointment-details">
                <small class="text-muted">
                  {{ appointment.date | date:'mediumDate' }} at {{ appointment.startTime }}
                </small>
              </div>
              <div class="appointment-time-left">
                <span class="badge bg-info">
                  In {{ getTimeUntilAppointment(appointment) }}
                </span>
              </div>
            </div>
            
            <div class="appointment-actions">
              <div class="btn-group-vertical" role="group">
                <app-chat-access 
                  [config]="{
                    appointmentId: appointment.id,
                    doctorId: appointment.doctor.id,
                    chatType: 'PRE_APPOINTMENT',
                    buttonText: 'Pre-Chat',
                    buttonClass: 'btn-outline-info',
                    size: 'sm',
                    showIcon: false
                  }">
                </app-chat-access>
                
                <app-chat-access 
                  [config]="{
                    doctorId: appointment.doctor.id,
                    chatType: 'GENERAL',
                    buttonText: 'General',
                    buttonClass: 'btn-outline-primary',
                    size: 'sm',
                    showIcon: false
                  }">
                </app-chat-access>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Data States -->
      <div *ngIf="recentChats.length === 0 && upcomingAppointments.length === 0" class="no-data">
        <div class="text-center py-4">
          <i class="fas fa-comments fa-3x text-muted mb-3"></i>
          <h6 class="text-muted">No recent conversations</h6>
          <p class="text-muted mb-3">Start chatting with your healthcare providers</p>
          <button 
            type="button" 
            class="btn btn-primary btn-sm"
            routerLink="/appointments/book">
            <i class="fas fa-calendar-plus me-2"></i>
            Book Appointment
          </button>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions mt-3 pt-3 border-top">
        <div class="row g-2">
          <div class="col-6">
            <button 
              type="button" 
              class="btn btn-outline-success btn-sm w-100"
              (click)="navigateToChat()">
              <i class="fas fa-plus me-1"></i>
              New Chat
            </button>
          </div>
          <div class="col-6">
            <app-chat-access 
              [config]="{
                chatType: 'URGENT',
                buttonText: 'Urgent',
                buttonClass: 'btn-outline-danger',
                size: 'sm'
              }"
              class="w-100">
            </app-chat-access>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

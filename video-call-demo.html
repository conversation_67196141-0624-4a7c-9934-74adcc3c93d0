<!DOCTYPE html>
<html>
<head>
    <title>🎥 HealthConnect Video Call Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7/bundles/stomp.umd.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .user-section { 
            display: flex; 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .user-panel { 
            flex: 1; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            padding: 15px; 
            background: #fafafa;
        }
        .patient-panel { border-color: #4CAF50; }
        .doctor-panel { border-color: #2196F3; }
        .video-container { 
            display: flex; 
            gap: 20px; 
            margin: 20px 0; 
        }
        .video-panel { 
            flex: 1; 
            border: 2px solid #333; 
            border-radius: 10px; 
            background: #000; 
            min-height: 300px; 
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        video { 
            width: 100%; 
            height: 100%; 
            border-radius: 8px; 
        }
        .controls { 
            display: flex; 
            gap: 10px; 
            margin: 10px 0; 
            flex-wrap: wrap;
        }
        button { 
            padding: 10px 15px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-weight: bold;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .status { 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .log { 
            height: 200px; 
            overflow-y: scroll; 
            border: 1px solid #ddd; 
            padding: 10px; 
            background: #f9f9f9; 
            font-family: monospace; 
            font-size: 12px;
        }
        .info-panel {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"] {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .user-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 HealthConnect Video Call Demo</h1>
        
        <div class="info-panel">
            <h3>📋 Demo Setup</h3>
            <p><strong>Patient:</strong> Emma Wilson (<EMAIL>) / demo123</p>
            <p><strong>Doctor:</strong> Dr. Michael Chen (<EMAIL>) / demo123</p>
            <p><strong>Room ID:</strong> room_ce48f8a711314d2b9e28dd07b931c5a3</p>
            <p><strong>Appointment ID:</strong> 6 | <strong>Consultation ID:</strong> 5</p>
        </div>

        <div class="user-section">
            <!-- Patient Panel -->
            <div class="user-panel patient-panel">
                <h3>👤 Patient: Emma Wilson</h3>
                <div class="user-info" id="patient-info">Not logged in</div>
                <div class="controls">
                    <button class="btn-primary" onclick="loginPatient()">Login as Patient</button>
                    <button class="btn-success" onclick="joinCallAsPatient()" id="patient-join" disabled>Join Video Call</button>
                    <button class="btn-danger" onclick="leaveCallAsPatient()" id="patient-leave" disabled>Leave Call</button>
                </div>
                <div class="status" id="patient-status">Ready to login</div>
            </div>

            <!-- Doctor Panel -->
            <div class="user-panel doctor-panel">
                <h3>👨‍⚕️ Doctor: Dr. Michael Chen</h3>
                <div class="user-info" id="doctor-info">Not logged in</div>
                <div class="controls">
                    <button class="btn-primary" onclick="loginDoctor()">Login as Doctor</button>
                    <button class="btn-success" onclick="joinCallAsDoctor()" id="doctor-join" disabled>Join Video Call</button>
                    <button class="btn-danger" onclick="leaveCallAsDoctor()" id="doctor-leave" disabled>Leave Call</button>
                </div>
                <div class="status" id="doctor-status">Ready to login</div>
            </div>
        </div>

        <!-- Video Streams -->
        <div class="video-container">
            <div class="video-panel">
                <video id="patient-video" autoplay muted playsinline></video>
                <div id="patient-placeholder">Patient Video (Not Connected)</div>
            </div>
            <div class="video-panel">
                <video id="doctor-video" autoplay playsinline></video>
                <div id="doctor-placeholder">Doctor Video (Not Connected)</div>
            </div>
        </div>

        <!-- WebRTC Controls -->
        <div class="controls">
            <button class="btn-warning" onclick="toggleMute()">Toggle Mute</button>
            <button class="btn-warning" onclick="toggleVideo()">Toggle Video</button>
            <button class="btn-primary" onclick="testWebRTCSignaling()">Test WebRTC Signaling</button>
            <button class="btn-primary" onclick="clearLog()">Clear Log</button>
        </div>

        <!-- Activity Log -->
        <h3>📊 Activity Log</h3>
        <div class="log" id="activity-log"></div>
    </div>

    <script>
        // Demo configuration
        const DEMO_CONFIG = {
            API_BASE: 'http://localhost:8080',
            WS_URL: 'http://localhost:8080/api/ws',
            ROOM_ID: 'room_ce48f8a711314d2b9e28dd07b931c5a3',
            CONSULTATION_ID: 5,
            PATIENT: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Emma Wilson',
                id: 11
            },
            DOCTOR: {
                email: '<EMAIL>',
                password: 'demo123',
                name: 'Dr. Michael Chen',
                id: 12
            }
        };

        // Global state
        let patientToken = null;
        let doctorToken = null;
        let patientStompClient = null;
        let doctorStompClient = null;
        let localStream = null;
        let remoteStream = null;
        let peerConnection = null;

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red' : type === 'success' ? 'color: green' : 'color: blue';
            logDiv.innerHTML += `<div style="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('activity-log').innerHTML = '';
        }

        // Update status functions
        function updatePatientStatus(message, type = 'info') {
            const statusDiv = document.getElementById('patient-status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateDoctorStatus(message, type = 'info') {
            const statusDiv = document.getElementById('doctor-status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Login functions
        async function loginPatient() {
            try {
                log('🔐 Patient login attempt...', 'info');
                const response = await fetch(`${DEMO_CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: DEMO_CONFIG.PATIENT.email,
                        password: DEMO_CONFIG.PATIENT.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    patientToken = data.token;
                    document.getElementById('patient-info').innerHTML = `✅ Logged in as ${DEMO_CONFIG.PATIENT.name}`;
                    document.getElementById('patient-join').disabled = false;
                    updatePatientStatus('Logged in successfully', 'connected');
                    log('✅ Patient login successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Patient login failed: ${error.message}`, 'error');
                updatePatientStatus('Login failed', 'error');
            }
        }

        async function loginDoctor() {
            try {
                log('🔐 Doctor login attempt...', 'info');
                const response = await fetch(`${DEMO_CONFIG.API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: DEMO_CONFIG.DOCTOR.email,
                        password: DEMO_CONFIG.DOCTOR.password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    doctorToken = data.token;
                    document.getElementById('doctor-info').innerHTML = `✅ Logged in as ${DEMO_CONFIG.DOCTOR.name}`;
                    document.getElementById('doctor-join').disabled = false;
                    updateDoctorStatus('Logged in successfully', 'connected');
                    log('✅ Doctor login successful', 'success');
                } else {
                    throw new Error('Login failed');
                }
            } catch (error) {
                log(`❌ Doctor login failed: ${error.message}`, 'error');
                updateDoctorStatus('Login failed', 'error');
            }
        }

        // WebSocket connection functions
        function connectWebSocket(token, userType, userId) {
            return new Promise((resolve, reject) => {
                const stompClient = new StompJs.Client({
                    webSocketFactory: () => new SockJS(DEMO_CONFIG.WS_URL),
                    connectHeaders: {
                        Authorization: `Bearer ${token}`
                    },
                    debug: (str) => {
                        log(`${userType} STOMP: ${str}`, 'info');
                    },
                    onConnect: (frame) => {
                        log(`✅ ${userType} WebSocket connected`, 'success');

                        // Subscribe to WebRTC room
                        stompClient.subscribe(`/topic/webrtc/${DEMO_CONFIG.ROOM_ID}`, (message) => {
                            const signal = JSON.parse(message.body);
                            log(`📡 ${userType} received WebRTC signal: ${signal.type}`, 'info');
                            handleWebRTCSignal(signal, userType);
                        });

                        resolve(stompClient);
                    },
                    onStompError: (frame) => {
                        log(`❌ ${userType} STOMP Error: ${frame.body}`, 'error');
                        reject(new Error('STOMP connection failed'));
                    },
                    onWebSocketError: (error) => {
                        log(`❌ ${userType} WebSocket Error: ${error}`, 'error');
                        reject(new Error('WebSocket connection failed'));
                    }
                });

                stompClient.activate();
            });
        }

        // Video call functions
        async function joinCallAsPatient() {
            try {
                updatePatientStatus('Connecting to video call...', 'connecting');
                log('🎥 Patient joining video call...', 'info');

                // Connect WebSocket
                patientStompClient = await connectWebSocket(patientToken, 'Patient', DEMO_CONFIG.PATIENT.id);

                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 },
                    audio: true
                });

                document.getElementById('patient-video').srcObject = localStream;
                document.getElementById('patient-placeholder').style.display = 'none';

                // Send join message
                patientStompClient.publish({
                    destination: `/app/webrtc/${DEMO_CONFIG.ROOM_ID}/join`,
                    body: JSON.stringify({
                        userId: DEMO_CONFIG.PATIENT.id,
                        userRole: 'PATIENT'
                    }),
                    headers: {
                        Authorization: `Bearer ${patientToken}`
                    }
                });

                document.getElementById('patient-join').disabled = true;
                document.getElementById('patient-leave').disabled = false;
                updatePatientStatus('Connected to video call', 'connected');
                log('✅ Patient joined video call successfully', 'success');

            } catch (error) {
                log(`❌ Patient failed to join call: ${error.message}`, 'error');
                updatePatientStatus('Failed to join call', 'error');
            }
        }

        async function joinCallAsDoctor() {
            try {
                updateDoctorStatus('Connecting to video call...', 'connecting');
                log('🎥 Doctor joining video call...', 'info');

                // Connect WebSocket
                doctorStompClient = await connectWebSocket(doctorToken, 'Doctor', DEMO_CONFIG.DOCTOR.id);

                // Send join message
                doctorStompClient.publish({
                    destination: `/app/webrtc/${DEMO_CONFIG.ROOM_ID}/join`,
                    body: JSON.stringify({
                        userId: DEMO_CONFIG.DOCTOR.id,
                        userRole: 'DOCTOR'
                    }),
                    headers: {
                        Authorization: `Bearer ${doctorToken}`
                    }
                });

                document.getElementById('doctor-join').disabled = true;
                document.getElementById('doctor-leave').disabled = false;
                updateDoctorStatus('Connected to video call', 'connected');
                log('✅ Doctor joined video call successfully', 'success');

            } catch (error) {
                log(`❌ Doctor failed to join call: ${error.message}`, 'error');
                updateDoctorStatus('Failed to join call', 'error');
            }
        }

        function leaveCallAsPatient() {
            if (patientStompClient) {
                patientStompClient.deactivate();
                patientStompClient = null;
            }
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            document.getElementById('patient-video').srcObject = null;
            document.getElementById('patient-placeholder').style.display = 'block';
            document.getElementById('patient-join').disabled = false;
            document.getElementById('patient-leave').disabled = true;
            updatePatientStatus('Disconnected from call', 'info');
            log('🔌 Patient left video call', 'info');
        }

        function leaveCallAsDoctor() {
            if (doctorStompClient) {
                doctorStompClient.deactivate();
                doctorStompClient = null;
            }
            document.getElementById('doctor-join').disabled = false;
            document.getElementById('doctor-leave').disabled = true;
            updateDoctorStatus('Disconnected from call', 'info');
            log('🔌 Doctor left video call', 'info');
        }

        function handleWebRTCSignal(signal, userType) {
            log(`🔄 Processing WebRTC signal for ${userType}: ${JSON.stringify(signal)}`, 'info');
            // This would handle actual WebRTC peer connection setup
            // For demo purposes, we're showing the signaling works
        }

        function toggleMute() {
            if (localStream) {
                const audioTrack = localStream.getAudioTracks()[0];
                if (audioTrack) {
                    audioTrack.enabled = !audioTrack.enabled;
                    log(`🔊 Audio ${audioTrack.enabled ? 'unmuted' : 'muted'}`, 'info');
                }
            }
        }

        function toggleVideo() {
            if (localStream) {
                const videoTrack = localStream.getVideoTracks()[0];
                if (videoTrack) {
                    videoTrack.enabled = !videoTrack.enabled;
                    log(`📹 Video ${videoTrack.enabled ? 'enabled' : 'disabled'}`, 'info');
                }
            }
        }

        function testWebRTCSignaling() {
            log('🧪 Testing WebRTC signaling infrastructure...', 'info');

            if (patientStompClient && patientStompClient.connected) {
                patientStompClient.publish({
                    destination: `/app/webrtc/${DEMO_CONFIG.ROOM_ID}/signal`,
                    body: JSON.stringify({
                        type: 'test-signal',
                        userId: DEMO_CONFIG.PATIENT.id,
                        data: 'Test signaling from patient'
                    }),
                    headers: {
                        Authorization: `Bearer ${patientToken}`
                    }
                });
                log('📤 Test signal sent from patient', 'success');
            }

            if (doctorStompClient && doctorStompClient.connected) {
                doctorStompClient.publish({
                    destination: `/app/webrtc/${DEMO_CONFIG.ROOM_ID}/signal`,
                    body: JSON.stringify({
                        type: 'test-signal',
                        userId: DEMO_CONFIG.DOCTOR.id,
                        data: 'Test signaling from doctor'
                    }),
                    headers: {
                        Authorization: `Bearer ${doctorToken}`
                    }
                });
                log('📤 Test signal sent from doctor', 'success');
            }
        }

        // Initialize page
        log('🚀 Video Call Demo initialized', 'info');
        log('📋 Please login both users and then join the video call', 'info');
        log('🎯 This demo will show WebSocket connections, WebRTC signaling, and video streaming', 'info');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Features Test - Meditech</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .chat-demo {
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background: #fafafa;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 18px;
            max-width: 70%;
            animation: slideIn 0.3s ease-out;
        }
        .message.own {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message.other {
            background: white;
            border: 1px solid #e0e0e0;
        }
        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #666;
        }
        .typing-dots {
            display: inline-block;
        }
        .typing-dots span {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #666;
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        .typing-dots span:nth-child(3) { animation-delay: 0s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .input-area {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .input-area input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
        .input-area button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-indicator.online { background: #28a745; }
        .status-indicator.offline { background: #6c757d; }
        .feature-test {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success { background: #d4edda; color: #155724; }
        .test-result.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Meditech Real-time Features Demo</h1>
        <p>This demo shows the real-time chat features that have been implemented.</p>
        
        <div class="feature-test">
            <h3>👤 User Presence Status</h3>
            <p>
                <span class="status-indicator online"></span>Dr. Smith - Online
                <span style="margin-left: 20px;"></span>
                <span class="status-indicator offline"></span>Patient John - Offline
            </p>
            <button class="test-button" onclick="togglePresence()">Toggle Presence</button>
            <div id="presence-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="feature-test">
            <h3>💬 Real-time Chat Demo</h3>
            <div class="chat-demo" id="chatDemo">
                <div class="message other">
                    <strong>Dr. Smith:</strong> Hello! How are you feeling today?
                    <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                        ✓✓ Read • 2:30 PM
                    </div>
                </div>
                <div class="message own">
                    <strong>You:</strong> I'm feeling much better, thank you!
                    <div style="font-size: 0.8em; color: rgba(255,255,255,0.7); margin-top: 5px;">
                        ✓✓ Delivered • 2:31 PM
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                Dr. Smith is typing
                <span class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </span>
            </div>
            
            <div class="input-area">
                <input type="text" id="messageInput" placeholder="Type a message..." 
                       onkeypress="handleKeyPress(event)" oninput="handleTyping()">
                <button onclick="sendMessage()">Send</button>
            </div>
            <button class="test-button" onclick="simulateTyping()">Simulate Typing Indicator</button>
            <button class="test-button" onclick="simulateIncomingMessage()">Simulate Incoming Message</button>
        </div>

        <div class="feature-test">
            <h3>📎 File Upload Demo</h3>
            <input type="file" id="fileInput" onchange="handleFileUpload(event)">
            <button class="test-button" onclick="simulateFileMessage()">Simulate File Message</button>
            <div id="file-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="feature-test">
            <h3>😀 Message Reactions Demo</h3>
            <div class="message other" id="reactableMessage">
                <strong>Dr. Smith:</strong> Your test results look great! 
                <div style="margin-top: 10px;">
                    <span onclick="addReaction('👍')" style="cursor: pointer; margin: 0 5px;">👍 2</span>
                    <span onclick="addReaction('❤️')" style="cursor: pointer; margin: 0 5px;">❤️ 1</span>
                    <span onclick="addReaction('🎉')" style="cursor: pointer; margin: 0 5px;">🎉</span>
                </div>
            </div>
            <button class="test-button" onclick="testReactions()">Test Reactions</button>
        </div>

        <div class="feature-test">
            <h3>📹 Video Call Demo</h3>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button class="test-button" onclick="simulateVideoCall()">Start Video Call</button>
                <button class="test-button" onclick="toggleMute()">Toggle Mute</button>
                <button class="test-button" onclick="toggleVideo()">Toggle Video</button>
                <button class="test-button" onclick="shareScreen()">Share Screen</button>
            </div>
            <div id="video-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="feature-test">
            <h3>🔄 WebSocket Connection Test</h3>
            <button class="test-button" onclick="testWebSocket()">Test WebSocket Connection</button>
            <div id="websocket-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let messageCount = 3;
        let isTyping = false;
        let typingTimeout;

        function togglePresence() {
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                indicator.classList.toggle('online');
                indicator.classList.toggle('offline');
            });
            showResult('presence-result', 'Presence status toggled!', 'success');
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function handleTyping() {
            if (!isTyping) {
                isTyping = true;
                console.log('Started typing...');
            }
            
            clearTimeout(typingTimeout);
            typingTimeout = setTimeout(() => {
                isTyping = false;
                console.log('Stopped typing...');
            }, 2000);
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                const chatDemo = document.getElementById('chatDemo');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message own';
                messageDiv.innerHTML = `
                    <strong>You:</strong> ${message}
                    <div style="font-size: 0.8em; color: rgba(255,255,255,0.7); margin-top: 5px;">
                        ✓ Sending... • ${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </div>
                `;
                
                chatDemo.appendChild(messageDiv);
                chatDemo.scrollTop = chatDemo.scrollHeight;
                input.value = '';
                
                // Simulate message status updates
                setTimeout(() => {
                    messageDiv.querySelector('div').innerHTML = `✓✓ Delivered • ${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                }, 1000);
                
                setTimeout(() => {
                    messageDiv.querySelector('div').innerHTML = `✓✓ Read • ${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                }, 3000);
            }
        }

        function simulateTyping() {
            const typingIndicator = document.getElementById('typingIndicator');
            typingIndicator.style.display = 'block';
            
            setTimeout(() => {
                typingIndicator.style.display = 'none';
            }, 3000);
        }

        function simulateIncomingMessage() {
            const chatDemo = document.getElementById('chatDemo');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message other';
            messageDiv.innerHTML = `
                <strong>Dr. Smith:</strong> This is a real-time message! Message #${messageCount++}
                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                    ✓✓ Just now
                </div>
            `;
            
            chatDemo.appendChild(messageDiv);
            chatDemo.scrollTop = chatDemo.scrollHeight;
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                showResult('file-result', `File selected: ${file.name} (${(file.size/1024).toFixed(1)} KB)`, 'success');
            }
        }

        function simulateFileMessage() {
            const chatDemo = document.getElementById('chatDemo');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message other';
            messageDiv.innerHTML = `
                <strong>Dr. Smith:</strong> 
                <div style="margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 8px;">
                    📎 <strong>test-results.pdf</strong> (245 KB)
                    <br><a href="#" style="color: #007bff;">Download</a>
                </div>
                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                    ✓✓ Just now
                </div>
            `;
            
            chatDemo.appendChild(messageDiv);
            chatDemo.scrollTop = chatDemo.scrollHeight;
        }

        function addReaction(emoji) {
            console.log(`Added reaction: ${emoji}`);
            showResult('file-result', `Added reaction: ${emoji}`, 'success');
        }

        function testReactions() {
            const message = document.getElementById('reactableMessage');
            const reactions = message.querySelector('div:last-child');
            reactions.innerHTML += ' <span onclick="addReaction(\'🚀\')" style="cursor: pointer; margin: 0 5px;">🚀 1</span>';
        }

        function simulateVideoCall() {
            showResult('video-result', 'Video call started! 📹 Audio: ON, Video: ON', 'success');
        }

        function toggleMute() {
            showResult('video-result', 'Audio toggled! 🔇', 'success');
        }

        function toggleVideo() {
            showResult('video-result', 'Video toggled! 📹', 'success');
        }

        function shareScreen() {
            showResult('video-result', 'Screen sharing started! 🖥️', 'success');
        }

        function testWebSocket() {
            showResult('websocket-result', 'Testing WebSocket connection...', 'success');
            
            // Simulate WebSocket connection test
            setTimeout(() => {
                try {
                    // This would normally connect to ws://localhost:8080/ws
                    showResult('websocket-result', '❌ Backend not running. Start backend with: cd backend && ./start-backend-simple.bat', 'error');
                } catch (error) {
                    showResult('websocket-result', `❌ Connection failed: ${error.message}`, 'error');
                }
            }, 2000);
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
            element.style.display = 'block';
            
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }

        // Auto-scroll chat demo
        document.getElementById('chatDemo').scrollTop = document.getElementById('chatDemo').scrollHeight;
        
        console.log('🚀 Real-time Features Demo Loaded!');
        console.log('📝 This demo shows the UI/UX of the implemented real-time features.');
        console.log('🔧 To test with real backend, start: cd backend && ./start-backend-simple.bat');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-container {
            display: flex;
            height: 500px;
        }
        .chat-list {
            width: 300px;
            border-right: 1px solid #eee;
            background: #f8f9fa;
        }
        .chat-window {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .chat-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #fff;
        }
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .message.sent {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message.received {
            background: #e9ecef;
            color: #333;
        }
        .message-input {
            display: flex;
            padding: 15px;
            border-top: 1px solid #eee;
            background: white;
        }
        .message-input input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
        .message-input button {
            margin-left: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        .chat-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }
        .chat-item:hover {
            background: #e9ecef;
        }
        .chat-item.active {
            background: #007bff;
            color: white;
        }
        .chat-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .chat-preview {
            font-size: 0.9em;
            color: #666;
        }
        .chat-item.active .chat-preview {
            color: #ccc;
        }
        .status {
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .features {
            padding: 20px;
            background: #f8f9fa;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .typing-indicator {
            font-style: italic;
            color: #666;
            padding: 5px 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💬 Real-time Chat System Test</h1>
            <p>Testing WebSocket-based patient-doctor communication</p>
        </div>

        <div class="status connecting" id="connectionStatus">
            Initializing chat system...
        </div>

        <div class="chat-container">
            <div class="chat-list">
                <div class="chat-item active" onclick="selectChat('doctor')">
                    <div class="chat-name">Dr. Sarah Johnson</div>
                    <div class="chat-preview">Cardiology specialist</div>
                </div>
                <div class="chat-item" onclick="selectChat('appointment')">
                    <div class="chat-name">Appointment Chat</div>
                    <div class="chat-preview">Pre-appointment discussion</div>
                </div>
                <div class="chat-item" onclick="selectChat('emergency')">
                    <div class="chat-name">Emergency Support</div>
                    <div class="chat-preview">24/7 medical support</div>
                </div>
            </div>

            <div class="chat-window">
                <div class="chat-header">
                    <strong id="currentChatName">Dr. Sarah Johnson</strong>
                    <div style="font-size: 0.9em; color: #666;">Cardiology Specialist • Online</div>
                </div>

                <div class="messages" id="messagesContainer">
                    <div class="message received">
                        <strong>Dr. Johnson:</strong> Hello! How can I help you today?
                    </div>
                    <div class="message sent">
                        <strong>You:</strong> Hi Doctor, I have some questions about my recent test results.
                    </div>
                    <div class="message received">
                        <strong>Dr. Johnson:</strong> Of course! I'd be happy to discuss your results. What specific concerns do you have?
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    Dr. Johnson is typing...
                </div>

                <div class="message-input">
                    <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleEnter(event)">
                    <button onclick="sendMessage()">Send</button>
                </div>
            </div>
        </div>

        <div class="features">
            <h3>✅ Chat System Features Implemented</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <strong>Real-time Messaging</strong><br>
                    WebSocket-based instant communication
                </div>
                <div class="feature-item">
                    <strong>Patient-Doctor Chats</strong><br>
                    Secure communication channels
                </div>
                <div class="feature-item">
                    <strong>Appointment Integration</strong><br>
                    Context-aware conversations
                </div>
                <div class="feature-item">
                    <strong>Message Status</strong><br>
                    Delivery and read receipts
                </div>
                <div class="feature-item">
                    <strong>Chat History</strong><br>
                    Persistent message storage
                </div>
                <div class="feature-item">
                    <strong>Typing Indicators</strong><br>
                    Real-time typing notifications
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentChat = 'doctor';
        let messageCount = 3;
        let isConnected = false;

        // Simulate WebSocket connection
        function initializeChat() {
            const status = document.getElementById('connectionStatus');
            
            // Simulate connection process
            setTimeout(() => {
                status.className = 'status connected';
                status.textContent = '✅ Chat system connected - WebSocket ready';
                isConnected = true;
            }, 1500);
        }

        function selectChat(chatType) {
            currentChat = chatType;
            
            // Update active chat item
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.chat-item').classList.add('active');
            
            // Update chat header and messages
            const chatNames = {
                'doctor': 'Dr. Sarah Johnson',
                'appointment': 'Appointment Chat',
                'emergency': 'Emergency Support'
            };
            
            document.getElementById('currentChatName').textContent = chatNames[chatType];
            
            // Clear and load appropriate messages
            loadChatMessages(chatType);
        }

        function loadChatMessages(chatType) {
            const container = document.getElementById('messagesContainer');
            
            const messages = {
                'doctor': [
                    { type: 'received', sender: 'Dr. Johnson', text: 'Hello! How can I help you today?' },
                    { type: 'sent', sender: 'You', text: 'Hi Doctor, I have some questions about my recent test results.' },
                    { type: 'received', sender: 'Dr. Johnson', text: 'Of course! I\'d be happy to discuss your results. What specific concerns do you have?' }
                ],
                'appointment': [
                    { type: 'received', sender: 'Dr. Johnson', text: 'Your appointment is scheduled for tomorrow at 2 PM.' },
                    { type: 'sent', sender: 'You', text: 'Thank you! Should I bring anything specific?' },
                    { type: 'received', sender: 'Dr. Johnson', text: 'Please bring your insurance card and any medications you\'re currently taking.' }
                ],
                'emergency': [
                    { type: 'received', sender: 'Emergency Support', text: 'Emergency support is available 24/7. How can we assist you?' },
                    { type: 'sent', sender: 'You', text: 'I need to speak with a doctor urgently.' },
                    { type: 'received', sender: 'Emergency Support', text: 'Connecting you with an available physician now...' }
                ]
            };
            
            container.innerHTML = '';
            messages[chatType].forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.type}`;
                messageDiv.innerHTML = `<strong>${msg.sender}:</strong> ${msg.text}`;
                container.appendChild(messageDiv);
            });
            
            container.scrollTop = container.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const container = document.getElementById('messagesContainer');
            
            if (!isConnected) {
                alert('Chat system is not connected yet. Please wait...');
                return;
            }
            
            if (input.value.trim()) {
                // Add user message
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message sent';
                messageDiv.innerHTML = `<strong>You:</strong> ${input.value}`;
                container.appendChild(messageDiv);
                
                // Show typing indicator
                const typingIndicator = document.getElementById('typingIndicator');
                typingIndicator.style.display = 'block';
                
                // Simulate response after delay
                setTimeout(() => {
                    typingIndicator.style.display = 'none';
                    
                    const responseDiv = document.createElement('div');
                    responseDiv.className = 'message received';
                    
                    const responses = [
                        'Thank you for your message. I understand your concern.',
                        'That\'s a great question. Let me provide you with some information.',
                        'I\'ll review this and get back to you shortly.',
                        'Based on what you\'ve described, here\'s what I recommend...',
                        'Please feel free to ask if you have any other questions.'
                    ];
                    
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    const senderName = currentChat === 'emergency' ? 'Emergency Support' : 'Dr. Johnson';
                    
                    responseDiv.innerHTML = `<strong>${senderName}:</strong> ${randomResponse}`;
                    container.appendChild(responseDiv);
                    
                    container.scrollTop = container.scrollHeight;
                }, 1000 + Math.random() * 2000);
                
                input.value = '';
                container.scrollTop = container.scrollHeight;
                messageCount++;
            }
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Simulate typing indicator
        document.getElementById('messageInput').addEventListener('input', function() {
            if (this.value.length > 0) {
                // In real implementation, this would send typing notification via WebSocket
                console.log('User is typing...');
            }
        });

        // Initialize chat system when page loads
        window.onload = initializeChat;
    </script>
</body>
</html>

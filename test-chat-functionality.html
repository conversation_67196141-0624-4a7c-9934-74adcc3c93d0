<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Functionality Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.1.1/bundles/stomp.umd.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>HealthConnect Chat Functionality Test</h1>
        
        <div class="test-section">
            <h3>1. Backend Connection Test</h3>
            <button onclick="testBackendConnection()">Test Backend API</button>
            <div id="backend-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>2. Authentication Test</h3>
            <input type="email" id="email" placeholder="Email (e.g., <EMAIL>)" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="password123">
            <button onclick="testAuthentication()">Login</button>
            <div id="auth-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>3. WebSocket Connection Test</h3>
            <button onclick="testWebSocketConnection()">Test WebSocket</button>
            <div id="websocket-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>4. Chat Creation Test</h3>
            <input type="number" id="participantId" placeholder="Participant ID (e.g., 2)" value="2">
            <button onclick="testChatCreation()">Create Chat</button>
            <div id="chat-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>5. Message Sending Test</h3>
            <input type="number" id="chatId" placeholder="Chat ID" value="1">
            <textarea id="messageContent" placeholder="Message content" rows="3">Hello, this is a test message!</textarea>
            <button onclick="testMessageSending()">Send Message</button>
            <div id="message-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>Debug Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let stompClient = null;
        let currentChatId = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function testBackendConnection() {
            log('Testing backend connection...');
            try {
                const response = await fetch('http://localhost:8080/api/test/health');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('backend-status', 'Backend is running!', 'success');
                    log('Backend connection successful');
                } else {
                    updateStatus('backend-status', `Backend error: ${response.status}`, 'error');
                    log(`Backend connection failed: ${response.status}`);
                }
            } catch (error) {
                updateStatus('backend-status', `Connection failed: ${error.message}`, 'error');
                log(`Backend connection error: ${error.message}`);
            }
        }

        async function testAuthentication() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log(`Attempting authentication for ${email}...`);
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    updateStatus('auth-status', `Authenticated as ${data.user.fullName}`, 'success');
                    log(`Authentication successful for ${data.user.fullName}`);
                } else {
                    const error = await response.text();
                    updateStatus('auth-status', `Authentication failed: ${error}`, 'error');
                    log(`Authentication failed: ${error}`);
                }
            } catch (error) {
                updateStatus('auth-status', `Authentication error: ${error.message}`, 'error');
                log(`Authentication error: ${error.message}`);
            }
        }

        function testWebSocketConnection() {
            if (!authToken) {
                updateStatus('websocket-status', 'Please authenticate first', 'warning');
                return;
            }

            log('Testing WebSocket connection...');
            
            try {
                const socket = new SockJS('http://localhost:8080/api/ws');
                stompClient = Stomp.over(socket);
                
                stompClient.connect(
                    { Authorization: `Bearer ${authToken}` },
                    function(frame) {
                        updateStatus('websocket-status', 'WebSocket connected successfully!', 'success');
                        log('WebSocket connection established');
                        
                        // Subscribe to error messages
                        stompClient.subscribe('/user/queue/errors', function(message) {
                            log(`WebSocket error: ${message.body}`, 'error');
                        });
                    },
                    function(error) {
                        updateStatus('websocket-status', `WebSocket connection failed: ${error}`, 'error');
                        log(`WebSocket connection failed: ${error}`, 'error');
                    }
                );
            } catch (error) {
                updateStatus('websocket-status', `WebSocket error: ${error.message}`, 'error');
                log(`WebSocket error: ${error.message}`, 'error');
            }
        }

        async function testChatCreation() {
            if (!authToken) {
                updateStatus('chat-status', 'Please authenticate first', 'warning');
                return;
            }

            const participantId = document.getElementById('participantId').value;
            log(`Creating chat with participant ${participantId}...`);

            try {
                const response = await fetch('http://localhost:8080/api/chats', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ participantId: parseInt(participantId) })
                });

                if (response.ok) {
                    const chat = await response.json();
                    currentChatId = chat.id;
                    document.getElementById('chatId').value = chat.id;
                    updateStatus('chat-status', `Chat created successfully! ID: ${chat.id}`, 'success');
                    log(`Chat created with ID: ${chat.id}`);
                } else {
                    const error = await response.text();
                    updateStatus('chat-status', `Chat creation failed: ${error}`, 'error');
                    log(`Chat creation failed: ${error}`, 'error');
                }
            } catch (error) {
                updateStatus('chat-status', `Chat creation error: ${error.message}`, 'error');
                log(`Chat creation error: ${error.message}`, 'error');
            }
        }

        function testMessageSending() {
            if (!authToken) {
                updateStatus('message-status', 'Please authenticate first', 'warning');
                return;
            }

            if (!stompClient || !stompClient.connected) {
                updateStatus('message-status', 'WebSocket not connected', 'warning');
                return;
            }

            const chatId = document.getElementById('chatId').value;
            const content = document.getElementById('messageContent').value;

            log(`Sending message to chat ${chatId}...`);

            try {
                // Subscribe to chat messages first
                stompClient.subscribe(`/topic/chat/${chatId}`, function(message) {
                    const messageData = JSON.parse(message.body);
                    log(`Received message: ${messageData.content} from ${messageData.sender.fullName}`);
                });

                // Send the message
                stompClient.send(`/app/chat/${chatId}/send`, 
                    { Authorization: `Bearer ${authToken}` },
                    JSON.stringify({ chatId: parseInt(chatId), content: content })
                );

                updateStatus('message-status', 'Message sent successfully!', 'success');
                log('Message sent via WebSocket');
            } catch (error) {
                updateStatus('message-status', `Message sending failed: ${error.message}`, 'error');
                log(`Message sending error: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        // Auto-test backend connection on page load
        window.onload = function() {
            log('Chat functionality test page loaded');
            testBackendConnection();
        };
    </script>
</body>
</html>

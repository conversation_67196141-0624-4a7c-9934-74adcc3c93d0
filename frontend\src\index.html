<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>HealthConnect - Medical Platform</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com https://sdk.twilio.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; img-src 'self' data: blob: http://localhost:4200; media-src 'self' blob: mediastream:; connect-src 'self' ws://localhost:8081 http://localhost:8081 https://global.vss.twilio.com https://sdkgw.us1.twilio.com wss://global.vss.twilio.com; worker-src 'self' blob:; frame-src 'self';">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script>
    // Polyfill for SockJS compatibility
    if (typeof global === 'undefined') {
      var global = window;
    }
  </script>
</head>
<body>
  <app-root></app-root>
</body>
</html>

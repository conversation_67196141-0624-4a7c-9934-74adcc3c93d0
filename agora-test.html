<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agora Video Test - HealthConnect</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-box {
            flex: 1;
            background: #000;
            border-radius: 10px;
            position: relative;
            min-height: 300px;
        }
        video {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }
        .video-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .logs {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Agora Video Calling Test - HealthConnect</h1>
        
        <div class="info">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Click "Join Channel" to start the video call</li>
                <li>Allow camera and microphone permissions when prompted</li>
                <li>Open this page in another browser tab/window to test two-way video</li>
                <li>Use different user IDs (change the UID below) for each participant</li>
            </ol>
        </div>

        <div class="status" id="status">Ready to connect</div>

        <div class="controls">
            <input type="text" id="channelName" placeholder="Channel Name" value="test-room-123" style="padding: 10px; margin-right: 10px;">
            <input type="number" id="uid" placeholder="User ID" value="12345" style="padding: 10px; margin-right: 10px;">
            <button class="btn-primary" onclick="joinChannel()">Join Channel</button>
            <button class="btn-danger" onclick="leaveChannel()">Leave Channel</button>
        </div>

        <div class="controls">
            <button class="btn-warning" onclick="toggleVideo()">Toggle Video</button>
            <button class="btn-warning" onclick="toggleAudio()">Toggle Audio</button>
        </div>

        <div class="video-container">
            <div class="video-box">
                <div class="video-label">Local Video (You)</div>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
            <div class="video-box">
                <div class="video-label">Remote Video (Other Participant)</div>
                <video id="remoteVideo" autoplay playsinline></video>
            </div>
        </div>

        <div class="logs" id="logs"></div>
    </div>

    <script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.20.2.js"></script>
    <script>
        // Agora configuration
        const APP_ID = '3b7704ab12634925a0ddc3f5a29bd453';
        
        let agoraClient = null;
        let localAudioTrack = null;
        let localVideoTrack = null;
        let remoteUsers = {};
        let isVideoEnabled = true;
        let isAudioEnabled = true;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function joinChannel() {
            try {
                const channelName = document.getElementById('channelName').value;
                const uid = parseInt(document.getElementById('uid').value);

                if (!channelName) {
                    alert('Please enter a channel name');
                    return;
                }

                updateStatus('Connecting...', 'connecting');
                log('Starting Agora Video initialization...');

                // Initialize Agora client
                agoraClient = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });
                log('Agora client created successfully');

                // Setup event handlers
                setupEventHandlers();

                // Join channel
                log(`Joining channel: ${channelName} with UID: ${uid}`);
                await agoraClient.join(APP_ID, channelName, null, uid);
                log('Successfully joined channel');

                // Create local tracks
                log('Creating local audio and video tracks...');
                [localAudioTrack, localVideoTrack] = await AgoraRTC.createMicrophoneAndCameraTracks();
                log('Local tracks created successfully');

                // Play local video
                const localVideo = document.getElementById('localVideo');
                localVideoTrack.play(localVideo);
                log('Local video playing');

                // Publish local tracks
                log('Publishing local tracks...');
                await agoraClient.publish([localAudioTrack, localVideoTrack]);
                log('Local tracks published successfully');

                updateStatus('✅ Connected and ready for video call!', 'connected');
                log('Agora Video initialization completed successfully', 'success');

            } catch (error) {
                log(`Error joining channel: ${error.message}`, 'error');
                updateStatus('Failed to connect', 'error');
                console.error('Join channel error:', error);
            }
        }

        function setupEventHandlers() {
            // Handle user published
            agoraClient.on('user-published', async (user, mediaType) => {
                log(`Remote user published: ${user.uid} (${mediaType})`);
                
                // Subscribe to the remote user
                await agoraClient.subscribe(user, mediaType);
                log(`Subscribed to remote user: ${user.uid} (${mediaType})`);
                
                // Store remote user
                remoteUsers[user.uid] = user;
                
                if (mediaType === 'video') {
                    const remoteVideo = document.getElementById('remoteVideo');
                    user.videoTrack.play(remoteVideo);
                    log('Remote video playing', 'success');
                    updateStatus('✅ Connected with remote participant!', 'connected');
                }
                
                if (mediaType === 'audio') {
                    user.audioTrack.play();
                    log('Remote audio playing', 'success');
                }
            });

            // Handle user unpublished
            agoraClient.on('user-unpublished', (user, mediaType) => {
                log(`Remote user unpublished: ${user.uid} (${mediaType})`);
                
                if (mediaType === 'video') {
                    const remoteVideo = document.getElementById('remoteVideo');
                    remoteVideo.srcObject = null;
                }
            });

            // Handle user left
            agoraClient.on('user-left', (user) => {
                log(`Remote user left: ${user.uid}`);
                delete remoteUsers[user.uid];
                
                const remoteVideo = document.getElementById('remoteVideo');
                remoteVideo.srcObject = null;
                updateStatus('Remote participant left the call', 'connecting');
            });

            // Handle connection state changes
            agoraClient.on('connection-state-change', (curState, revState) => {
                log(`Connection state changed: ${curState} (from ${revState})`);
                if (curState === 'DISCONNECTED') {
                    updateStatus('Connection lost', 'error');
                }
            });
        }

        async function leaveChannel() {
            try {
                log('Leaving channel...');

                // Close local tracks
                if (localAudioTrack) {
                    localAudioTrack.close();
                    localAudioTrack = null;
                }
                
                if (localVideoTrack) {
                    localVideoTrack.close();
                    localVideoTrack = null;
                }

                // Leave the channel
                if (agoraClient) {
                    await agoraClient.leave();
                    agoraClient = null;
                }

                // Clear videos
                document.getElementById('localVideo').srcObject = null;
                document.getElementById('remoteVideo').srcObject = null;

                // Clear remote users
                remoteUsers = {};

                updateStatus('Disconnected', 'info');
                log('Left channel successfully', 'success');

            } catch (error) {
                log(`Error leaving channel: ${error.message}`, 'error');
                console.error('Leave channel error:', error);
            }
        }

        async function toggleVideo() {
            try {
                if (!localVideoTrack) return;

                if (isVideoEnabled) {
                    await localVideoTrack.setEnabled(false);
                    log('Video disabled');
                } else {
                    await localVideoTrack.setEnabled(true);
                    log('Video enabled');
                }

                isVideoEnabled = !isVideoEnabled;
            } catch (error) {
                log(`Error toggling video: ${error.message}`, 'error');
            }
        }

        async function toggleAudio() {
            try {
                if (!localAudioTrack) return;

                if (isAudioEnabled) {
                    await localAudioTrack.setEnabled(false);
                    log('Audio disabled');
                } else {
                    await localAudioTrack.setEnabled(true);
                    log('Audio enabled');
                }

                isAudioEnabled = !isAudioEnabled;
            } catch (error) {
                log(`Error toggling audio: ${error.message}`, 'error');
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            leaveChannel();
        });
    </script>
</body>
</html>

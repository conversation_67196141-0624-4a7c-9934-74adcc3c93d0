# Application Configuration
spring.application.name=healthconnect-backend
server.port=8081

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:healthconnect;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=password
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for debugging)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# JWT Configuration
jwt.secret=mySecretKeyForHealthConnectPlatformThatIsLongEnoughForSecurity
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Google Gemini AI Configuration
google.ai.api.key=AIzaSyCFWc5GegGFmlMpD7-gNyfXO_eiU24PklQ
google.ai.model=gemini-1.5-flash

# Logging Configuration
logging.level.com.healthconnect=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web.cors=WARN
logging.level.org.springframework.web.socket=INFO

# Error handling
server.error.include-message=always
server.error.include-binding-errors=always

# Allow circular references temporarily
spring.main.allow-circular-references=true

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.file.upload-dir=uploads
app.file.max-size=10485760
app.file.allowed-types=image/jpeg,image/png,image/gif,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,audio/mpeg,audio/wav,video/mp4

# WebSocket Configuration
spring.websocket.max-connections=1000
spring.websocket.heartbeat-interval=30000

# Presence Service Configuration
app.presence.cleanup-interval=300000
app.presence.inactive-timeout=600000

# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:4200
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# Agora Video Configuration
# Get these from: https://console.agora.io/
agora.app.id=********************************
agora.app.certificate=********************************


